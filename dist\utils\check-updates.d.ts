/**
 * Update Checking System
 *
 * Automatically checks for newer versions and displays update notifications
 * Includes frequency control and package manager detection
 */
interface UpdateInfo {
    hasUpdate: boolean;
    currentVersion: string;
    latestVersion?: string;
    updateCommand?: string;
    releaseNotes?: string;
}
interface LastUpdateCheck {
    timestamp: number;
    lastCheckedVersion: string;
    latestVersion?: string;
}
/**
 * Check for updates with frequency control
 */
export declare function checkForUpdates(): Promise<UpdateInfo | null>;
/**
 * Force check for updates (ignore frequency control)
 */
export declare function forceCheckForUpdates(): Promise<UpdateInfo>;
/**
 * Get last update check information
 */
export declare function getLastUpdateCheck(): LastUpdateCheck | null;
/**
 * Clear update check cache
 */
export declare function clearUpdateCheckCache(): void;
/**
 * Disable update checks
 */
export declare function disableUpdateChecks(): void;
export {};
//# sourceMappingURL=check-updates.d.ts.map