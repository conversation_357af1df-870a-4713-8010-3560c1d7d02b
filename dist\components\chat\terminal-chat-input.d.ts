/**
 * Advanced Terminal Chat Input System
 *
 * Provides sophisticated input handling with multiline editing, file suggestions,
 * slash commands, history navigation, and tab completion
 */
interface TerminalChatInputProps {
    value: string;
    onChange: (value: string) => void;
    onSubmit: (value: string) => void;
    disabled?: boolean;
    placeholder?: string;
    multiline?: boolean;
    showSuggestions?: boolean;
}
export declare function TerminalChatInput({ value, onChange, onSubmit, disabled, placeholder, multiline, showSuggestions }: TerminalChatInputProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=terminal-chat-input.d.ts.map