/**
 * Model Information System
 *
 * Comprehensive model metadata including context lengths, capabilities,
 * and support for latest models (o1, o3, GPT-4.1, etc.)
 */
/**
 * Comprehensive model database with detailed information
 */
export const MODEL_DATABASE = {
    // OpenAI GPT-4 Family
    'gpt-4': {
        id: 'gpt-4',
        name: 'GPT-4',
        contextLength: 8192,
        supportsImages: false,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-4-turbo': {
        id: 'gpt-4-turbo',
        name: 'GPT-4 Turbo',
        contextLength: 128000,
        supportsImages: true,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-4-turbo-preview': {
        id: 'gpt-4-turbo-preview',
        name: 'GPT-4 Turbo Preview',
        contextLength: 128000,
        supportsImages: true,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-4o': {
        id: 'gpt-4o',
        name: 'GPT-4o',
        contextLength: 128000,
        supportsImages: true,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-4o-mini': {
        id: 'gpt-4o-mini',
        name: 'GPT-4o Mini',
        contextLength: 128000,
        supportsImages: true,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-4-32k': {
        id: 'gpt-4-32k',
        name: 'GPT-4 32K',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-4-vision-preview': {
        id: 'gpt-4-vision-preview',
        name: 'GPT-4 Vision Preview',
        contextLength: 128000,
        supportsImages: true,
        supportsTools: true,
        provider: 'openai'
    },
    // OpenAI GPT-3.5 Family
    'gpt-3.5-turbo': {
        id: 'gpt-3.5-turbo',
        name: 'GPT-3.5 Turbo',
        contextLength: 4096,
        supportsImages: false,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-3.5-turbo-16k': {
        id: 'gpt-3.5-turbo-16k',
        name: 'GPT-3.5 Turbo 16K',
        contextLength: 16384,
        supportsImages: false,
        supportsTools: true,
        provider: 'openai'
    },
    'gpt-3.5-turbo-instruct': {
        id: 'gpt-3.5-turbo-instruct',
        name: 'GPT-3.5 Turbo Instruct',
        contextLength: 4096,
        supportsImages: false,
        supportsTools: false,
        provider: 'openai'
    },
    // OpenAI o1 Family (Latest reasoning models)
    'o1-preview': {
        id: 'o1-preview',
        name: 'o1 Preview',
        contextLength: 128000,
        supportsImages: false,
        supportsTools: false,
        provider: 'openai'
    },
    'o1-mini': {
        id: 'o1-mini',
        name: 'o1 Mini',
        contextLength: 128000,
        supportsImages: false,
        supportsTools: false,
        provider: 'openai'
    },
    // Google Gemini Family
    'gemini-pro': {
        id: 'gemini-pro',
        name: 'Gemini Pro',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'gemini'
    },
    'gemini-pro-vision': {
        id: 'gemini-pro-vision',
        name: 'Gemini Pro Vision',
        contextLength: 32768,
        supportsImages: true,
        supportsTools: true,
        provider: 'gemini'
    },
    'gemini-ultra': {
        id: 'gemini-ultra',
        name: 'Gemini Ultra',
        contextLength: 32768,
        supportsImages: true,
        supportsTools: true,
        provider: 'gemini'
    },
    // Mistral Family
    'mistral-tiny': {
        id: 'mistral-tiny',
        name: 'Mistral Tiny',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'mistral'
    },
    'mistral-small': {
        id: 'mistral-small',
        name: 'Mistral Small',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'mistral'
    },
    'mistral-medium': {
        id: 'mistral-medium',
        name: 'Mistral Medium',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'mistral'
    },
    'mistral-large-latest': {
        id: 'mistral-large-latest',
        name: 'Mistral Large Latest',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'mistral'
    },
    // DeepSeek Family
    'deepseek-chat': {
        id: 'deepseek-chat',
        name: 'DeepSeek Chat',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'deepseek'
    },
    'deepseek-coder': {
        id: 'deepseek-coder',
        name: 'DeepSeek Coder',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'deepseek'
    },
    // xAI Family
    'grok-beta': {
        id: 'grok-beta',
        name: 'Grok Beta',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'xai'
    },
    // Groq Family
    'llama3-8b-8192': {
        id: 'llama3-8b-8192',
        name: 'Llama 3 8B',
        contextLength: 8192,
        supportsImages: false,
        supportsTools: true,
        provider: 'groq'
    },
    'llama3-70b-8192': {
        id: 'llama3-70b-8192',
        name: 'Llama 3 70B',
        contextLength: 8192,
        supportsImages: false,
        supportsTools: true,
        provider: 'groq'
    },
    'mixtral-8x7b-32768': {
        id: 'mixtral-8x7b-32768',
        name: 'Mixtral 8x7B',
        contextLength: 32768,
        supportsImages: false,
        supportsTools: true,
        provider: 'groq'
    },
    'gemma-7b-it': {
        id: 'gemma-7b-it',
        name: 'Gemma 7B IT',
        contextLength: 8192,
        supportsImages: false,
        supportsTools: true,
        provider: 'groq'
    },
    // Ollama Models (Local)
    'llama2': {
        id: 'llama2',
        name: 'Llama 2',
        contextLength: 4096,
        supportsImages: false,
        supportsTools: true,
        provider: 'ollama'
    },
    'llama2:13b': {
        id: 'llama2:13b',
        name: 'Llama 2 13B',
        contextLength: 4096,
        supportsImages: false,
        supportsTools: true,
        provider: 'ollama'
    },
    'llama2:70b': {
        id: 'llama2:70b',
        name: 'Llama 2 70B',
        contextLength: 4096,
        supportsImages: false,
        supportsTools: true,
        provider: 'ollama'
    },
    'codellama': {
        id: 'codellama',
        name: 'Code Llama',
        contextLength: 16384,
        supportsImages: false,
        supportsTools: true,
        provider: 'ollama'
    },
    'mistral': {
        id: 'mistral',
        name: 'Mistral',
        contextLength: 8192,
        supportsImages: false,
        supportsTools: true,
        provider: 'ollama'
    }
};
/**
 * Get model information by ID
 */
export function getModelInfo(modelId) {
    return MODEL_DATABASE[modelId] || null;
}
/**
 * Get all models for a specific provider
 */
export function getModelsForProvider(provider) {
    return Object.values(MODEL_DATABASE).filter(model => model.provider === provider);
}
/**
 * Get models that support images
 */
export function getImageSupportedModels() {
    return Object.values(MODEL_DATABASE).filter(model => model.supportsImages);
}
/**
 * Get models that support tools/function calling
 */
export function getToolSupportedModels() {
    return Object.values(MODEL_DATABASE).filter(model => model.supportsTools);
}
/**
 * Get models with high context length (>= 32K tokens)
 */
export function getHighContextModels() {
    return Object.values(MODEL_DATABASE).filter(model => model.contextLength >= 32768);
}
/**
 * Search models by name or ID
 */
export function searchModels(query) {
    const lowerQuery = query.toLowerCase();
    return Object.values(MODEL_DATABASE).filter(model => model.id.toLowerCase().includes(lowerQuery) ||
        model.name.toLowerCase().includes(lowerQuery));
}
/**
 * Get model capabilities summary
 */
export function getModelCapabilities(modelId) {
    const model = getModelInfo(modelId);
    if (!model)
        return null;
    let contextSize;
    if (model.contextLength <= 4096)
        contextSize = 'small';
    else if (model.contextLength <= 16384)
        contextSize = 'medium';
    else if (model.contextLength <= 32768)
        contextSize = 'large';
    else
        contextSize = 'xlarge';
    return {
        hasImages: model.supportsImages,
        hasTools: model.supportsTools,
        contextSize,
        provider: model.provider
    };
}
//# sourceMappingURL=model-info.js.map