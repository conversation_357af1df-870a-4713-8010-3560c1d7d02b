/**
 * Platform Command Adaptation System
 *
 * Converts Unix commands to Windows equivalents and handles
 * platform-specific command variations
 */
/**
 * Adapt command for current platform
 */
export declare function adaptCommandForPlatform(command: string[]): string[];
/**
 * Check if command is available on current platform
 */
export declare function isCommandAvailable(command: string): boolean;
/**
 * Get platform-specific command suggestions
 */
export declare function getCommandSuggestions(command: string): string[];
/**
 * Get command help for platform
 */
export declare function getCommandHelp(command: string): string;
/**
 * Normalize path separators for platform
 */
export declare function normalizePath(path: string): string;
/**
 * Get shell command for platform
 */
export declare function getShellCommand(): string[];
/**
 * Escape command argument for platform
 */
export declare function escapeArgument(arg: string): string;
//# sourceMappingURL=platform-commands.d.ts.map