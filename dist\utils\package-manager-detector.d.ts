/**
 * Package Manager Detection System
 *
 * Auto-detects package managers based on installation path and environment
 * Supports npm, pnpm, bun, yarn, and deno
 */
export type AgentName = 'npm' | 'pnpm' | 'bun' | 'yarn' | 'deno';
/**
 * Detect package manager by analyzing the installation path
 */
export declare function detectInstallerByPath(): Promise<AgentName | undefined>;
/**
 * Get package manager version
 */
export declare function getPackageManagerVersion(pm: AgentName): Promise<string | null>;
/**
 * Get installation command for package manager
 */
export declare function getInstallCommand(pm: AgentName, packageName: string, global?: boolean): string;
/**
 * Get update command for package manager
 */
export declare function getUpdateCommand(pm: AgentName, packageName: string, global?: boolean): string;
/**
 * Get uninstall command for package manager
 */
export declare function getUninstallCommand(pm: AgentName, packageName: string, global?: boolean): string;
/**
 * Detect package manager from lock files
 */
export declare function detectFromLockFiles(projectPath?: string): AgentName | undefined;
/**
 * Get package manager information
 */
export declare function getPackageManagerInfo(pm?: AgentName): Promise<{
    name: AgentName;
    version: string | null;
    available: boolean;
    installCommand: string;
    updateCommand: string;
}>;
/**
 * Get all available package managers
 */
export declare function getAllPackageManagers(): Promise<Array<{
    name: AgentName;
    version: string | null;
    available: boolean;
}>>;
/**
 * Recommend best package manager
 */
export declare function recommendPackageManager(): Promise<{
    recommended: AgentName;
    reason: string;
    alternatives: AgentName[];
}>;
//# sourceMappingURL=package-manager-detector.d.ts.map