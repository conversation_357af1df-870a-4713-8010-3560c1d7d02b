/**
 * Model Management System
 *
 * Handles dynamic model discovery, validation, and management
 * Provides caching for performance and context length calculation
 */
import { createOpenAIClient } from './openai-client.js';
// Model cache to avoid repeated API calls
const modelCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
/**
 * Fetch available models from provider
 */
export async function fetchModels(provider) {
    // Check cache first
    const cached = modelCache.get(provider);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.models;
    }
    try {
        const client = createOpenAIClient({ provider });
        const response = await client.models.list();
        const models = response.data
            .map(model => model.id)
            .filter(id => id && typeof id === 'string')
            .sort();
        // Cache the results
        modelCache.set(provider, {
            models,
            timestamp: Date.now()
        });
        return models;
    }
    catch (error) {
        console.warn(`Warning: Could not fetch models for provider ${provider}:`, error);
        // Return fallback models for known providers
        return getFallbackModels(provider);
    }
}
/**
 * Get fallback models when API is unavailable
 */
function getFallbackModels(provider) {
    const fallbackModels = {
        openai: [
            'gpt-4',
            'gpt-4-turbo',
            'gpt-4-turbo-preview',
            'gpt-3.5-turbo',
            'gpt-3.5-turbo-16k'
        ],
        azure: [
            'gpt-4',
            'gpt-4-32k',
            'gpt-35-turbo',
            'gpt-35-turbo-16k'
        ],
        gemini: [
            'gemini-pro',
            'gemini-pro-vision',
            'gemini-ultra'
        ],
        ollama: [
            'llama2',
            'llama2:13b',
            'llama2:70b',
            'codellama',
            'mistral',
            'neural-chat'
        ],
        mistral: [
            'mistral-tiny',
            'mistral-small',
            'mistral-medium',
            'mistral-large-latest'
        ],
        deepseek: [
            'deepseek-chat',
            'deepseek-coder'
        ],
        xai: [
            'grok-beta'
        ],
        groq: [
            'llama3-8b-8192',
            'llama3-70b-8192',
            'mixtral-8x7b-32768',
            'gemma-7b-it'
        ],
        arceeai: [
            'arcee-nova',
            'arcee-spark'
        ],
        openrouter: [
            'openai/gpt-4',
            'openai/gpt-3.5-turbo',
            'anthropic/claude-3-opus',
            'meta-llama/llama-2-70b-chat'
        ]
    };
    return fallbackModels[provider.toLowerCase()] || ['gpt-4'];
}
/**
 * Validate if model exists for provider
 */
export async function validateModel(model, provider) {
    try {
        const models = await fetchModels(provider);
        return models.includes(model);
    }
    catch (error) {
        console.warn(`Warning: Could not validate model ${model} for provider ${provider}`);
        return true; // Assume valid if we can't check
    }
}
/**
 * Get model information including context length and capabilities
 */
export function getModelInfo(model, provider) {
    const contextLengths = {
        // OpenAI models
        'gpt-4': 8192,
        'gpt-4-turbo': 128000,
        'gpt-4-turbo-preview': 128000,
        'gpt-4-32k': 32768,
        'gpt-3.5-turbo': 4096,
        'gpt-3.5-turbo-16k': 16384,
        'o1-preview': 128000,
        'o1-mini': 128000,
        // Gemini models
        'gemini-pro': 32768,
        'gemini-pro-vision': 32768,
        'gemini-ultra': 32768,
        // Mistral models
        'mistral-large-latest': 32768,
        'mistral-medium': 32768,
        'mistral-small': 32768,
        'mistral-tiny': 32768,
        // Groq models
        'llama3-8b-8192': 8192,
        'llama3-70b-8192': 8192,
        'mixtral-8x7b-32768': 32768,
        'gemma-7b-it': 8192,
        // Default fallback
        'default': 4096
    };
    const supportsImages = [
        'gpt-4-vision-preview',
        'gpt-4-turbo',
        'gpt-4o',
        'gemini-pro-vision'
    ].includes(model) || provider === 'openrouter';
    const supportsTools = ![
        'gpt-3.5-turbo-instruct',
        'text-davinci-003'
    ].includes(model);
    return {
        id: model,
        name: model,
        contextLength: contextLengths[model] || contextLengths.default,
        supportsImages,
        supportsTools,
        provider: provider
    };
}
/**
 * Calculate token usage for context management
 */
export function estimateTokens(text) {
    // Rough estimation: 1 token ≈ 4 characters for English text
    return Math.ceil(text.length / 4);
}
/**
 * Check if context fits within model limits
 */
export function checkContextLimit(content, model, provider, buffer = 1000) {
    const modelInfo = getModelInfo(model, provider);
    const tokens = estimateTokens(content);
    const limit = modelInfo.contextLength - buffer; // Reserve buffer for response
    return {
        fits: tokens <= limit,
        tokens,
        limit: modelInfo.contextLength
    };
}
/**
 * Get recommended models for provider
 */
export function getRecommendedModels(provider) {
    const recommendations = {
        openai: ['gpt-4-turbo', 'gpt-4', 'gpt-3.5-turbo'],
        azure: ['gpt-4', 'gpt-35-turbo'],
        gemini: ['gemini-pro'],
        ollama: ['llama2', 'codellama'],
        mistral: ['mistral-large-latest', 'mistral-medium'],
        deepseek: ['deepseek-chat'],
        xai: ['grok-beta'],
        groq: ['llama3-70b-8192', 'mixtral-8x7b-32768'],
        arceeai: ['arcee-nova'],
        openrouter: ['openai/gpt-4', 'anthropic/claude-3-opus']
    };
    return recommendations[provider.toLowerCase()] || ['gpt-4'];
}
/**
 * Clear model cache
 */
export function clearModelCache() {
    modelCache.clear();
}
/**
 * Get cached models without API call
 */
export function getCachedModels(provider) {
    const cached = modelCache.get(provider);
    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return cached.models;
    }
    return null;
}
//# sourceMappingURL=model-utils.js.map