/**
 * Multi-Modal Input Processing System
 *
 * Handles various input types including text, files, images, and URLs
 * Provides unified processing for different content types
 */
import type { MessageContent } from '../types/index.js';
export interface ProcessedInput {
    type: 'text' | 'file' | 'image' | 'url' | 'mixed';
    content: MessageContent[];
    metadata: {
        originalInput: string;
        processedFiles: string[];
        processedImages: string[];
        processedUrls: string[];
        totalSize: number;
    };
}
export interface FileProcessingOptions {
    maxFileSize: number;
    allowedExtensions: string[];
    includeMetadata: boolean;
    truncateContent: boolean;
    maxContentLength: number;
}
export interface ImageProcessingOptions {
    maxImageSize: number;
    supportedFormats: string[];
    includeMetadata: boolean;
}
/**
 * Process multi-modal input string
 */
export declare function processMultiModalInput(input: string, fileOptions?: Partial<FileProcessingOptions>, imageOptions?: Partial<ImageProcessingOptions>): ProcessedInput;
//# sourceMappingURL=multimodal-input.d.ts.map