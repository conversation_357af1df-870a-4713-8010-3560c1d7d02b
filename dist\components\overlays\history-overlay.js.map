{"version": 3, "file": "history-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/history-overlay.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAChE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,UAAU,EAAE,aAAa,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAC;AASpG,MAAM,UAAU,cAAc,CAAC,EAC7B,eAAe,EACf,OAAO,EACP,OAAO,EACa;IACpB,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IACtD,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;IACnD,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpD,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAiB,EAAE,CAAC,CAAC;IAC3E,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAM,IAAI,CAAC,CAAC;IAE9C,yBAAyB;IACzB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;YAC7B,MAAM,YAAY,GAAG,eAAe,EAAE,CAAC;YAEvC,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,oBAAoB;YAC3D,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACpB,cAAc,CAAC,EAAE,CAAC,CAAC;YACnB,aAAa,CAAC,KAAK,CAAC,CAAC;QACvB,CAAC;IACH,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;IAEd,oDAAoD;IACpD,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,WAAW,CAAC,IAAI,EAAE,EAAE,CAAC;YACvB,MAAM,OAAO,GAAG,aAAa,CAAC,WAAW,CAAC,CAAC;YAC3C,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,oBAAoB;YAC3D,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;aAAM,CAAC;YACN,MAAM,OAAO,GAAG,UAAU,EAAE,CAAC;YAC7B,kBAAkB,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YACtC,gBAAgB,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,gBAAgB;QAChB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,UAAU,IAAI,WAAW,EAAE,CAAC;gBAC9B,qBAAqB;gBACrB,cAAc,CAAC,EAAE,CAAC,CAAC;gBACnB,aAAa,CAAC,KAAK,CAAC,CAAC;YACvB,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;YACD,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,aAAa,CAAC,CAAC,UAAU,CAAC,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBAChC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC1C,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,aAAa,CAAC,KAAK,CAAC,CAAC;gBACrB,OAAO;YACT,CAAC;YAED,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACpC,cAAc,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;gBACrC,OAAO;YACT,CAAC;QACH,CAAC;aAAM,CAAC;YACN,kBAAkB;YAClB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;gBAChB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;gBACjD,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBAClB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;gBAC1E,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC;gBAClD,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACjB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,aAAa,GAAG,EAAE,CAAC,CAAC,CAAC;gBAC3E,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,IAAI,EAAE,CAAC;gBACb,gBAAgB,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO;YACT,CAAC;YAED,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;gBACZ,gBAAgB,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAC7C,OAAO;YACT,CAAC;YAED,iBAAiB;YACjB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;gBACf,MAAM,aAAa,GAAG,eAAe,CAAC,aAAa,CAAC,CAAC;gBACrD,IAAI,aAAa,EAAE,CAAC;oBAClB,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBACvC,OAAO,EAAE,CAAC;gBACZ,CAAC;gBACD,OAAO;YACT,CAAC;YAED,eAAe;YACf,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;gBAClB,aAAa,CAAC,IAAI,CAAC,CAAC;gBACpB,OAAO;YACT,CAAC;QACH,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,SAAiB,EAAU,EAAE;QAChE,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC;QACjC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,MAAM,GAAG,GAAG,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QAC9C,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAE5D,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;YACnB,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;aAAM,IAAI,QAAQ,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,WAAW,CAAC;QACrB,CAAC;aAAM,IAAI,QAAQ,GAAG,CAAC,EAAE,CAAC;YACxB,OAAO,GAAG,QAAQ,WAAW,CAAC;QAChC,CAAC;aAAM,CAAC;YACN,OAAO,IAAI,CAAC,kBAAkB,EAAE,CAAC;QACnC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,OAAiB,EAAU,EAAE;QACpE,IAAI,OAAO,KAAK,IAAI;YAAE,OAAO,GAAG,CAAC;QACjC,IAAI,OAAO,KAAK,KAAK;YAAE,OAAO,GAAG,CAAC;QAClC,OAAO,GAAG,CAAC;IACb,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IACF,QAAQ,EAAC,UAAU,EACnB,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EACP,KAAK,EAAE,CAAC,EACR,MAAM,EAAE,CAAC,EACT,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,MAAM,EAClB,eAAe,EAAC,OAAO,EACvB,aAAa,EAAC,QAAQ,aAGtB,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,aACpE,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,sCAEhB,EACN,KAAK,IAAI,CACR,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,aACf,KAAK,CAAC,aAAa,oBAAW,KAAK,CAAC,cAAc,qBAAY,KAAK,CAAC,kBAAkB,mBAClF,GACH,CACP,IACG,EAGN,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM,aAC7F,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,yBAAgB,EAClC,KAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,YACzC,WAAW,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,oCAAoC,CAAC,GACpE,EACN,WAAW,IAAI,CACd,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,UAAU,EAAE,CAAC,kBAC5B,eAAe,CAAC,MAAM,iBACnB,CACR,IACG,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,aAAa,EAAC,QAAQ,YAC/D,eAAe,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAC9B,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YACf,WAAW,CAAC,CAAC,CAAC,yCAAyC,CAAC,CAAC,CAAC,+BAA+B,GACrF,CACR,CAAC,CAAC,CAAC,CACF,4BAEG,CAAC,GAAG,EAAE;wBACL,MAAM,UAAU,GAAG,EAAE,CAAC,CAAC,4BAA4B;wBACnD,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;wBAC3E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,MAAM,EAAE,UAAU,GAAG,UAAU,CAAC,CAAC;wBAC3E,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;wBAEjE,OAAO,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;4BACvC,MAAM,WAAW,GAAG,UAAU,GAAG,KAAK,CAAC;4BACvC,MAAM,UAAU,GAAG,WAAW,KAAK,aAAa,CAAC;4BAEjD,OAAO,CACL,MAAC,GAAG,IAA6C,YAAY,EAAE,CAAC,aAC9D,KAAC,GAAG,IAAC,KAAK,EAAE,CAAC,YACX,KAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,YACzF,mBAAmB,CAAC,KAAK,CAAC,OAAO,CAAC,GAC9B,GACH,EACN,KAAC,GAAG,IAAC,KAAK,EAAE,EAAE,YACZ,KAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,YACzF,eAAe,CAAC,KAAK,CAAC,SAAS,CAAC,GAC5B,GACH,EACN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,YACd,KAAC,IAAI,IACH,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EACrC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAChD,IAAI,EAAE,UAAU,YAEf,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,GAC9E,GACH,KAnBE,GAAG,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,OAAO,EAAE,CAoBzC,CACP,CAAC;wBACJ,CAAC,CAAC,CAAC;oBACL,CAAC,CAAC,EAAE,GACH,CACJ,GACG,EAGN,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,aACpE,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,4HAEX,EACN,eAAe,CAAC,MAAM,GAAG,CAAC,IAAI,CAC7B,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,aACf,aAAa,GAAG,CAAC,OAAG,eAAe,CAAC,MAAM,IACtC,GACH,CACP,IACG,IACF,CACP,CAAC;AACJ,CAAC"}