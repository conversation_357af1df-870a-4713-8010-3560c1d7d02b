/**
 * Help Information Overlay
 *
 * Comprehensive help system with keyboard shortcuts, commands, and usage tips
 * Provides contextual help and feature explanations
 */
interface HelpOverlayProps {
    onClose: () => void;
    visible: boolean;
}
export declare function HelpOverlay({ onClose, visible }: HelpOverlayProps): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=help-overlay.d.ts.map