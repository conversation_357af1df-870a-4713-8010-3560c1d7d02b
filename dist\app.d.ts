/**
 * Main Application Class
 *
 * Orchestrates the entire Kritrima AI CLI application
 * Handles git validation, safety checks, and UI initialization
 */
import type { AppConfig } from './types/index.js';
export declare class App {
    private config;
    private inkInstance;
    constructor(config: AppConfig);
    /**
     * Start the application
     */
    start(): Promise<void>;
    /**
     * Stop the application
     */
    stop(): Promise<void>;
    /**
     * Perform startup checks and validations
     */
    private performStartupChecks;
    /**
     * Validate application configuration
     */
    private validateConfiguration;
    /**
     * Check API connectivity (non-blocking)
     */
    private checkAPIConnectivity;
    /**
     * Initialize terminal UI
     */
    private initializeUI;
    /**
     * Show startup message
     */
    private showStartupMessage;
    /**
     * Setup signal handlers for graceful shutdown
     */
    private setupSignalHandlers;
    /**
     * Handle application exit
     */
    private handleExit;
    /**
     * Get current configuration
     */
    getConfig(): AppConfig;
    /**
     * Update configuration
     */
    updateConfig(updates: Partial<AppConfig>): void;
    /**
     * Restart application with new configuration
     */
    restart(newConfig?: Partial<AppConfig>): Promise<void>;
}
/**
 * Create and start application instance
 */
export declare function createApp(config: AppConfig): Promise<App>;
/**
 * Application factory with error handling
 */
export declare function startApplication(config: AppConfig): Promise<void>;
//# sourceMappingURL=app.d.ts.map