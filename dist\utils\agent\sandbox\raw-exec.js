/**
 * Raw Command Execution
 *
 * Fallback execution without sandboxing for platforms that don't support
 * advanced sandboxing features. Still applies basic security checks.
 */
import { spawn } from 'child_process';
import { resolve } from 'path';
import { existsSync } from 'fs';
import { logInfo, logError } from '../../logger/log.js';
/**
 * Execute command without sandboxing
 */
export async function exec(input, config) {
    const startTime = Date.now();
    logInfo('Executing command (raw)', {
        command: input.command,
        workdir: input.workdir,
        timeout: input.timeout
    });
    try {
        // Basic security checks
        validateCommand(input, config);
        // Resolve working directory
        const workdir = input.workdir ? resolve(input.workdir) : process.cwd();
        if (!existsSync(workdir)) {
            throw new Error(`Working directory does not exist: ${workdir}`);
        }
        // Execute command
        const result = await executeCommand(input.command, workdir, input.timeout || 30000);
        const duration = Date.now() - startTime;
        logInfo('Command execution completed', {
            command: input.command,
            exitCode: result.exitCode,
            duration,
            success: result.exitCode === 0
        });
        return {
            success: result.exitCode === 0,
            output: result.stdout + (result.stderr ? `\nSTDERR:\n${result.stderr}` : ''),
            error: result.stderr || undefined,
            exitCode: result.exitCode,
            duration,
            command: input.command,
            workdir
        };
    }
    catch (error) {
        const duration = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Command execution failed', error instanceof Error ? error : new Error(errorMessage));
        return {
            success: false,
            output: '',
            error: errorMessage,
            exitCode: -1,
            duration,
            command: input.command,
            workdir: input.workdir || process.cwd()
        };
    }
}
/**
 * Validate command for basic security
 */
function validateCommand(input, config) {
    const command = input.command;
    if (!command || command.length === 0) {
        throw new Error('Command is required');
    }
    const commandName = command[0].toLowerCase();
    // Check against dangerous commands
    const dangerousCommands = config.dangerousCommands || [
        'rm', 'del', 'format', 'fdisk', 'mkfs',
        'sudo', 'su', 'chmod', 'chown',
        'shutdown', 'reboot', 'halt',
        'dd', 'shred', 'wipe'
    ];
    if (dangerousCommands.includes(commandName)) {
        throw new Error(`Dangerous command blocked: ${commandName}`);
    }
    // Check for suspicious patterns
    const fullCommand = command.join(' ');
    const suspiciousPatterns = [
        /rm\s+-rf\s+\//, // rm -rf /
        />\s*\/dev\//, // Redirect to device files
        /curl.*\|\s*sh/, // Curl pipe to shell
        /wget.*\|\s*sh/, // Wget pipe to shell
        /eval\s*\$/, // Eval with variables
        /exec\s*\$/, // Exec with variables
    ];
    for (const pattern of suspiciousPatterns) {
        if (pattern.test(fullCommand)) {
            throw new Error(`Suspicious command pattern detected: ${pattern.source}`);
        }
    }
    // Validate working directory
    if (input.workdir) {
        const workdir = resolve(input.workdir);
        // Prevent access to sensitive directories
        const restrictedPaths = [
            '/etc',
            '/boot',
            '/sys',
            '/proc',
            '/dev',
            'C:\\Windows\\System32',
            'C:\\Windows\\SysWOW64'
        ];
        for (const restricted of restrictedPaths) {
            if (workdir.startsWith(restricted)) {
                throw new Error(`Access to restricted directory blocked: ${workdir}`);
            }
        }
    }
}
/**
 * Execute command using child_process
 */
function executeCommand(command, workdir, timeout) {
    return new Promise((resolve, reject) => {
        const [cmd, ...args] = command;
        const child = spawn(cmd, args, {
            cwd: workdir,
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: process.platform === 'win32',
            timeout
        });
        let stdout = '';
        let stderr = '';
        // Collect stdout
        child.stdout?.on('data', (data) => {
            stdout += data.toString();
        });
        // Collect stderr
        child.stderr?.on('data', (data) => {
            stderr += data.toString();
        });
        // Handle process completion
        child.on('close', (code) => {
            resolve({
                stdout: stdout.trim(),
                stderr: stderr.trim(),
                exitCode: code || 0
            });
        });
        // Handle process errors
        child.on('error', (error) => {
            reject(new Error(`Process error: ${error.message}`));
        });
        // Handle timeout
        setTimeout(() => {
            if (!child.killed) {
                child.kill('SIGTERM');
                // Force kill after additional timeout
                setTimeout(() => {
                    if (!child.killed) {
                        child.kill('SIGKILL');
                    }
                }, 5000);
                reject(new Error(`Command timed out after ${timeout}ms`));
            }
        }, timeout);
    });
}
/**
 * Check if command is safe for execution
 */
export function isCommandSafe(command, config) {
    try {
        validateCommand({ command }, config);
        return true;
    }
    catch (error) {
        return false;
    }
}
/**
 * Get command execution environment info
 */
export function getExecutionEnvironment() {
    return {
        platform: process.platform,
        shell: process.platform === 'win32' ? 'cmd' : 'sh',
        sandboxing: false,
        restrictions: [
            'Basic command validation',
            'Dangerous command blocking',
            'Suspicious pattern detection',
            'Restricted directory protection'
        ]
    };
}
/**
 * Test command execution capabilities
 */
export async function testExecution() {
    const capabilities = [];
    const limitations = [];
    try {
        // Test basic command execution
        const testResult = await executeCommand(['echo', 'test'], process.cwd(), 5000);
        if (testResult.exitCode === 0 && testResult.stdout.includes('test')) {
            capabilities.push('Basic command execution');
        }
    }
    catch (error) {
        limitations.push('Basic command execution failed');
    }
    // Check platform capabilities
    if (process.platform === 'win32') {
        capabilities.push('Windows command execution');
        limitations.push('No Unix-style sandboxing');
    }
    else {
        capabilities.push('Unix-style command execution');
        limitations.push('No advanced sandboxing (raw execution)');
    }
    // Security limitations
    limitations.push('No filesystem isolation');
    limitations.push('No network isolation');
    limitations.push('No resource limits');
    return {
        success: capabilities.length > 0,
        capabilities,
        limitations
    };
}
//# sourceMappingURL=raw-exec.js.map