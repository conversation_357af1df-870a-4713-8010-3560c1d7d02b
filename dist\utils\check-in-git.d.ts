/**
 * Git Repository Detection
 *
 * Provides reliable Git repository detection using git commands
 * Fast synchronous operation that works across different Git versions
 */
/**
 * Check if current directory is inside a Git repository
 */
export declare function checkInGit(workdir?: string): boolean;
/**
 * Get Git repository root directory
 */
export declare function getGitRoot(workdir?: string): string | null;
/**
 * Get current Git branch name
 */
export declare function getCurrentBranch(workdir?: string): string | null;
/**
 * Check if Git repository has uncommitted changes
 */
export declare function hasUncommittedChanges(workdir?: string): boolean;
/**
 * Get Git repository status information
 */
export declare function getGitStatus(workdir?: string): {
    inGit: boolean;
    root?: string;
    branch?: string;
    hasChanges?: boolean;
    isClean?: boolean;
};
/**
 * Check if file is tracked by Git
 */
export declare function isFileTracked(filePath: string, workdir?: string): boolean;
/**
 * Get list of modified files
 */
export declare function getModifiedFiles(workdir?: string): string[];
/**
 * Get list of staged files
 */
export declare function getStagedFiles(workdir?: string): string[];
/**
 * Get list of untracked files
 */
export declare function getUntrackedFiles(workdir?: string): string[];
/**
 * Get comprehensive Git file status
 */
export declare function getFileStatus(workdir?: string): {
    modified: string[];
    staged: string[];
    untracked: string[];
    total: number;
};
//# sourceMappingURL=check-in-git.d.ts.map