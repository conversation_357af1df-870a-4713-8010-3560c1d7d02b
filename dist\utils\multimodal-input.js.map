{"version": 3, "file": "multimodal-input.js", "sourceRoot": "", "sources": ["../../src/utils/multimodal-input.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,IAAI,CAAC;AACxD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAClD,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,iBAAiB,CAAC;AA6BpD,MAAM,oBAAoB,GAA0B;IAClD,WAAW,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IACtC,iBAAiB,EAAE;QACjB,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI;QACzE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;QACxE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ;QACpE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS;KACnE;IACD,eAAe,EAAE,IAAI;IACrB,eAAe,EAAE,IAAI;IACrB,gBAAgB,EAAE,KAAK,CAAC,iBAAiB;CAC1C,CAAC;AAEF,MAAM,qBAAqB,GAA2B;IACpD,YAAY,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,OAAO;IACvC,gBAAgB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;IACpE,eAAe,EAAE,IAAI;CACtB,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,sBAAsB,CACpC,KAAa,EACb,cAA8C,EAAE,EAChD,eAAgD,EAAE;IAElD,MAAM,gBAAgB,GAAG,EAAE,GAAG,oBAAoB,EAAE,GAAG,WAAW,EAAE,CAAC;IACrE,MAAM,iBAAiB,GAAG,EAAE,GAAG,qBAAqB,EAAE,GAAG,YAAY,EAAE,CAAC;IAExE,OAAO,CAAC,8BAA8B,EAAE;QACtC,WAAW,EAAE,KAAK,CAAC,MAAM;QACzB,WAAW,EAAE,gBAAgB;QAC7B,YAAY,EAAE,iBAAiB;KAChC,CAAC,CAAC;IAEH,MAAM,MAAM,GAAmB;QAC7B,IAAI,EAAE,MAAM;QACZ,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE;YACR,aAAa,EAAE,KAAK;YACpB,cAAc,EAAE,EAAE;YAClB,eAAe,EAAE,EAAE;YACnB,aAAa,EAAE,EAAE;YACjB,SAAS,EAAE,CAAC;SACb;KACF,CAAC;IAEF,IAAI,CAAC;QACH,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAE3C,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;gBACrB,KAAK,MAAM;oBACT,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;wBAClB,IAAI,EAAE,YAAY;wBAClB,IAAI,EAAE,OAAO,CAAC,OAAO;qBACtB,CAAC,CAAC;oBACH,MAAM;gBAER,KAAK,MAAM;oBACT,MAAM,WAAW,GAAG,oBAAoB,CAAC,OAAO,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAC;oBAC5E,IAAI,WAAW,EAAE,CAAC;wBAChB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;wBACjC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBACrD,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,WAAW,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC;oBAC7D,CAAC;oBACD,MAAM;gBAER,KAAK,OAAO;oBACV,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAC;oBAC/E,IAAI,YAAY,EAAE,CAAC;wBACjB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;wBAClC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBACtD,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC;oBACtE,CAAC;oBACD,MAAM;gBAER,KAAK,KAAK;oBACR,MAAM,UAAU,GAAG,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBACxD,IAAI,UAAU,EAAE,CAAC;wBACf,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBAChC,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;wBACpD,MAAM,CAAC,QAAQ,CAAC,SAAS,IAAI,UAAU,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,CAAC;oBAC5D,CAAC;oBACD,MAAM;YACV,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;YACzC,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC;YAC1C,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7C,MAAM,CAAC,IAAI,GAAG,OAAO,CAAC;QACxB,CAAC;QAED,OAAO,CAAC,6BAA6B,EAAE;YACrC,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,YAAY,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM;YACnC,KAAK,EAAE,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM;YAC5C,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,MAAM;YAC9C,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,MAAM;YAC1C,SAAS,EAAE,MAAM,CAAC,QAAQ,CAAC,SAAS;SACrC,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAEhB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,qCAAqC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAE3G,yBAAyB;QACzB,OAAO;YACL,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;YAC9C,QAAQ,EAAE;gBACR,aAAa,EAAE,KAAK;gBACpB,cAAc,EAAE,EAAE;gBAClB,eAAe,EAAE,EAAE;gBACnB,aAAa,EAAE,EAAE;gBACjB,SAAS,EAAE,KAAK,CAAC,MAAM;aACxB;SACF,CAAC;IACJ,CAAC;AACH,CAAC;AAUD,SAAS,kBAAkB,CAAC,KAAa;IACvC,MAAM,QAAQ,GAAmB,EAAE,CAAC;IACpC,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,6CAA6C;IAC7C,MAAM,QAAQ,GAAG;QACf,EAAE,IAAI,EAAE,MAAe,EAAE,KAAK,EAAE,YAAY,EAAE;QAC9C,EAAE,IAAI,EAAE,OAAgB,EAAE,KAAK,EAAE,2BAA2B,EAAE;QAC9D,EAAE,IAAI,EAAE,KAAc,EAAE,KAAK,EAAE,oBAAoB,EAAE;KACtD,CAAC;IAEF,MAAM,OAAO,GAA2F,EAAE,CAAC;IAE3G,mBAAmB;IACnB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,IAAI,KAAK,CAAC;QACV,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,cAAc;QAE3C,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;YACpD,OAAO,CAAC,IAAI,CAAC;gBACX,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,KAAK,EAAE,KAAK,CAAC,KAAK;gBAClB,GAAG,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM;gBAClC,OAAO,EAAE,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrC,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;wBACrC,KAAK,CAAC,CAAC,CAAC;aACjB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,2BAA2B;IAC3B,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;IAE1C,mBAAmB;IACnB,KAAK,MAAM,KAAK,IAAI,OAAO,EAAE,CAAC;QAC5B,wBAAwB;QACxB,IAAI,UAAU,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;YAC7B,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;YACpE,IAAI,WAAW,EAAE,CAAC;gBAChB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAED,gBAAgB;QAChB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC5D,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC;IACzB,CAAC;IAED,qBAAqB;IACrB,IAAI,UAAU,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC;QAC9B,MAAM,WAAW,GAAG,KAAK,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,IAAI,EAAE,CAAC;QACvD,IAAI,WAAW,EAAE,CAAC;YAChB,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,6DAA6D;IAC7D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAClD,CAAC;IAED,OAAO,QAAQ,CAAC;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAC3B,QAAgB,EAChB,OAA8B;IAE9B,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9B,QAAQ,CAAC,gBAAgB,EAAE,IAAI,KAAK,CAAC,wBAAwB,YAAY,EAAE,CAAC,CAAC,CAAC;YAC9E,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,oBAAoB,QAAQ,GAAG;aACtC,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QACrC,MAAM,OAAO,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACpD,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QAExC,kBAAkB;QAClB,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;YACrC,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,oBAAoB,QAAQ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,MAAM;aACzH,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,IAAI,OAAO,CAAC,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACzF,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,2BAA2B,QAAQ,KAAK,OAAO,IAAI;aAC1D,CAAC;QACJ,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,GAAG,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,CAAC;QAElD,wBAAwB;QACxB,IAAI,OAAO,CAAC,eAAe,IAAI,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,gBAAgB,EAAE,CAAC;YACzE,OAAO,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,CAAC,gBAAgB,CAAC,GAAG,4BAA4B,CAAC;QAC1F,CAAC;QAED,4BAA4B;QAC5B,IAAI,YAAY,GAAG,OAAO,CAAC;QAC3B,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;YAC5B,MAAM,QAAQ,GAAG;gBACf,SAAS,QAAQ,EAAE;gBACnB,SAAS,YAAY,EAAE;gBACvB,SAAS,KAAK,CAAC,IAAI,QAAQ;gBAC3B,aAAa,KAAK,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE;gBACxC,KAAK;aACN,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACb,YAAY,GAAG,QAAQ,GAAG,IAAI,GAAG,OAAO,CAAC;QAC3C,CAAC;QAED,OAAO;YACL,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,YAAY;SACnB,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,kCAAkC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACxG,OAAO;YACL,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,wBAAwB,QAAQ,GAAG;SAC1C,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAC5B,SAAiB,EACjB,OAA+B;IAE/B,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;QAExC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAC9B,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,qBAAqB,SAAS,GAAG;aACxC,CAAC;QACJ,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QACrC,MAAM,QAAQ,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,CAAC;QACrD,MAAM,SAAS,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;QAEzC,kBAAkB;QAClB,IAAI,KAAK,CAAC,IAAI,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;YACtC,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,qBAAqB,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;aAC5H,CAAC;QACJ,CAAC;QAED,eAAe;QACf,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,OAAO;gBACL,IAAI,EAAE,YAAY;gBAClB,IAAI,EAAE,8BAA8B,SAAS,KAAK,QAAQ,IAAI;aAC/D,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,MAAM,SAAS,GAAG,YAAY,CAAC,YAAY,CAAC,CAAC;QAC7C,MAAM,UAAU,GAAG,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,QAAQ,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEvC,OAAO;YACL,IAAI,EAAE,OAAO;YACb,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE,QAAQ;gBACpB,IAAI,EAAE,UAAU;aACjB;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,QAAQ,CAAC,mCAAmC,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QACzG,OAAO;YACL,IAAI,EAAE,YAAY;YAClB,IAAI,EAAE,yBAAyB,SAAS,GAAG;SAC5C,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,mBAAmB,CAAC,GAAW;IACtC,uCAAuC;IACvC,yEAAyE;IACzE,OAAO;QACL,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE,SAAS,GAAG,GAAG;KACtB,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,SAAiB;IACpC,MAAM,SAAS,GAA2B;QACxC,MAAM,EAAE,YAAY;QACpB,OAAO,EAAE,YAAY;QACrB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,WAAW;QACnB,MAAM,EAAE,WAAW;QACnB,OAAO,EAAE,YAAY;KACtB,CAAC;IAEF,OAAO,SAAS,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,0BAA0B,CAAC;AAC1E,CAAC"}