/**
 * OpenAI Client Factory
 *
 * Creates provider-specific client instances with proper configuration
 * Handles both standard OpenAI and Azure OpenAI configurations
 */
import { OpenAI } from 'openai';
interface ClientOptions {
    provider?: string;
    apiKey?: string;
    baseURL?: string;
    timeout?: number;
    organization?: string;
    project?: string;
}
/**
 * Create OpenAI client instance for specified provider
 */
export declare function createOpenAIClient(options?: ClientOptions): OpenAI;
/**
 * Test client connection
 */
export declare function testClientConnection(provider?: string): Promise<{
    success: boolean;
    error?: string;
}>;
/**
 * Get client with retry logic
 */
export declare function createClientWithRetry(options?: ClientOptions, maxRetries?: number): Promise<OpenAI>;
/**
 * Create multiple clients for different providers
 */
export declare function createMultiProviderClients(providers: string[]): Record<string, OpenAI>;
/**
 * Get client configuration for debugging
 */
export declare function getClientConfig(provider?: string): {
    provider: string;
    baseURL: string;
    hasApiKey: boolean;
    timeout: number;
};
/**
 * Validate client configuration
 */
export declare function validateClientConfig(provider?: string): {
    valid: boolean;
    errors: string[];
};
export {};
//# sourceMappingURL=openai-client.d.ts.map