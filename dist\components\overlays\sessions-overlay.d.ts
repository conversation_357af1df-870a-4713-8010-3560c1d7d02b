/**
 * Sessions Management Overlay
 *
 * Browse, load, and manage saved conversation sessions
 * Supports session preview, deletion, and restoration
 */
import type { SessionData } from '../../types/index.js';
interface SessionsOverlayProps {
    onLoadSession: (sessionData: SessionData) => void;
    onClose: () => void;
    visible: boolean;
}
export declare function SessionsOverlay({ onLoadSession, onClose, visible }: SessionsOverlayProps): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=sessions-overlay.d.ts.map