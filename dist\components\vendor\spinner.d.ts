/**
 * Advanced Spinner Component
 *
 * Provides various spinner animations with customizable styles and text
 * Supports different spinner types and loading states
 */
export type SpinnerType = 'dots' | 'line' | 'pipe' | 'star' | 'arrow' | 'bounce' | 'pulse' | 'wave' | 'clock' | 'earth';
export interface SpinnerProps {
    type?: SpinnerType;
    text?: string;
    color?: string;
    interval?: number;
    enabled?: boolean;
}
export declare function Spinner({ type, text, color, interval, enabled }: SpinnerProps): import("react/jsx-runtime").JSX.Element | null;
/**
 * Loading Spinner with customizable message
 */
export interface LoadingSpinnerProps extends SpinnerProps {
    message?: string;
    showElapsed?: boolean;
    startTime?: number;
}
export declare function LoadingSpinner({ message, showElapsed, startTime, ...spinnerProps }: LoadingSpinnerProps): import("react/jsx-runtime").JSX.Element;
/**
 * Multi-step progress spinner
 */
export interface ProgressSpinnerProps {
    steps: string[];
    currentStep: number;
    type?: SpinnerType;
    color?: string;
    completedColor?: string;
    enabled?: boolean;
}
export declare function ProgressSpinner({ steps, currentStep, type, color, completedColor, enabled }: ProgressSpinnerProps): import("react/jsx-runtime").JSX.Element;
/**
 * Spinner with status indicator
 */
export interface StatusSpinnerProps extends SpinnerProps {
    status: 'loading' | 'success' | 'error' | 'warning';
    successText?: string;
    errorText?: string;
    warningText?: string;
}
export declare function StatusSpinner({ status, text, successText, errorText, warningText, ...spinnerProps }: StatusSpinnerProps): import("react/jsx-runtime").JSX.Element;
/**
 * Animated dots for typing indicator
 */
export interface TypingIndicatorProps {
    color?: string;
    enabled?: boolean;
}
export declare function TypingIndicator({ color, enabled }: TypingIndicatorProps): import("react/jsx-runtime").JSX.Element | null;
/**
 * Pulsing indicator
 */
export interface PulseIndicatorProps {
    color?: string;
    character?: string;
    interval?: number;
    enabled?: boolean;
}
export declare function PulseIndicator({ color, character, interval, enabled }: PulseIndicatorProps): import("react/jsx-runtime").JSX.Element | null;
/**
 * Custom spinner with user-defined frames
 */
export interface CustomSpinnerProps {
    frames: string[];
    text?: string;
    color?: string;
    interval?: number;
    enabled?: boolean;
}
export declare function CustomSpinner({ frames, text, color, interval, enabled }: CustomSpinnerProps): import("react/jsx-runtime").JSX.Element | null;
//# sourceMappingURL=spinner.d.ts.map