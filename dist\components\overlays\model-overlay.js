import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * Model Selection Overlay
 *
 * Provides dynamic model and provider selection with real-time model fetching
 * Supports tab navigation between provider and model selection
 */
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { fetchModels } from '../../utils/model-utils.js';
import { getAvailableProviders } from '../../utils/providers.js';
import { getApiKey } from '../../utils/config.js';
export function ModelOverlay({ currentProvider, currentModel, onProviderChange, onModelChange, onClose, visible }) {
    const [activeTab, setActiveTab] = useState('provider');
    const [selectedProviderIndex, setSelectedProviderIndex] = useState(0);
    const [selectedModelIndex, setSelectedModelIndex] = useState(0);
    const [availableModels, setAvailableModels] = useState([]);
    const [loadingModels, setLoadingModels] = useState(false);
    const [modelError, setModelError] = useState(null);
    // Get available providers
    const providers = getAvailableProviders();
    // Initialize selected provider index
    useEffect(() => {
        const providerIndex = providers.findIndex(p => p === currentProvider);
        if (providerIndex >= 0) {
            setSelectedProviderIndex(providerIndex);
        }
    }, [currentProvider, providers]);
    // Load models when provider changes
    useEffect(() => {
        if (activeTab === 'model') {
            loadModelsForProvider(providers[selectedProviderIndex]);
        }
    }, [activeTab, selectedProviderIndex, providers]);
    // Initialize selected model index
    useEffect(() => {
        const modelIndex = availableModels.findIndex(m => m === currentModel);
        if (modelIndex >= 0) {
            setSelectedModelIndex(modelIndex);
        }
        else {
            setSelectedModelIndex(0);
        }
    }, [currentModel, availableModels]);
    /**
     * Load models for selected provider
     */
    const loadModelsForProvider = useCallback(async (provider) => {
        setLoadingModels(true);
        setModelError(null);
        try {
            // Check if API key is available
            const apiKey = getApiKey(provider);
            if (!apiKey) {
                setModelError(`No API key configured for ${provider}`);
                setAvailableModels([]);
                return;
            }
            const models = await fetchModels(provider);
            setAvailableModels(models);
            if (models.length === 0) {
                setModelError(`No models available for ${provider}`);
            }
        }
        catch (error) {
            setModelError(error instanceof Error ? error.message : 'Failed to load models');
            setAvailableModels([]);
        }
        finally {
            setLoadingModels(false);
        }
    }, []);
    // Handle keyboard input
    useInput((input, key) => {
        if (!visible)
            return;
        // Close overlay
        if (key.escape) {
            onClose();
            return;
        }
        // Tab navigation
        if (key.tab) {
            setActiveTab(activeTab === 'provider' ? 'model' : 'provider');
            return;
        }
        // Navigation within tabs
        if (key.upArrow) {
            if (activeTab === 'provider') {
                setSelectedProviderIndex(Math.max(0, selectedProviderIndex - 1));
            }
            else {
                setSelectedModelIndex(Math.max(0, selectedModelIndex - 1));
            }
            return;
        }
        if (key.downArrow) {
            if (activeTab === 'provider') {
                setSelectedProviderIndex(Math.min(providers.length - 1, selectedProviderIndex + 1));
            }
            else {
                setSelectedModelIndex(Math.min(availableModels.length - 1, selectedModelIndex + 1));
            }
            return;
        }
        // Selection
        if (key.return) {
            if (activeTab === 'provider') {
                const selectedProvider = providers[selectedProviderIndex];
                onProviderChange(selectedProvider);
                setActiveTab('model'); // Switch to model selection
            }
            else {
                const selectedModel = availableModels[selectedModelIndex];
                if (selectedModel) {
                    onModelChange(selectedModel);
                    onClose();
                }
            }
            return;
        }
        // Quick provider selection by letter
        if (activeTab === 'provider' && input && !key.ctrl && !key.meta) {
            const letter = input.toLowerCase();
            const providerIndex = providers.findIndex(p => p.toLowerCase().startsWith(letter));
            if (providerIndex >= 0) {
                setSelectedProviderIndex(providerIndex);
            }
        }
    });
    if (!visible) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", top: 2, left: 2, right: 2, bottom: 2, borderStyle: "double", borderColor: "cyan", backgroundColor: "black", flexDirection: "column", children: [_jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsx(Text, { color: "cyan", bold: true, children: "Model & Provider Selection" }), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { color: "gray", children: "Tab: Switch tabs \u2022 \u2191\u2193: Navigate \u2022 Enter: Select \u2022 Esc: Close" }) })] }), _jsxs(Box, { paddingX: 2, paddingY: 1, children: [_jsx(Box, { marginRight: 4, children: _jsx(Text, { color: activeTab === 'provider' ? 'white' : 'gray', backgroundColor: activeTab === 'provider' ? 'blue' : undefined, bold: activeTab === 'provider', children: ' Provider ' }) }), _jsx(Box, { children: _jsx(Text, { color: activeTab === 'model' ? 'white' : 'gray', backgroundColor: activeTab === 'model' ? 'blue' : undefined, bold: activeTab === 'model', children: ' Model ' }) })] }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingBottom: 1, children: activeTab === 'provider' ? (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, marginBottom: 1, children: "Available Providers:" }), providers.map((provider, index) => {
                            const isSelected = index === selectedProviderIndex;
                            const isCurrent = provider === currentProvider;
                            const hasApiKey = !!getApiKey(provider);
                            return (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: isSelected ? 'black' : hasApiKey ? 'green' : 'red', backgroundColor: isSelected ? 'cyan' : undefined, bold: isSelected || isCurrent, children: [isSelected ? '► ' : '  ', provider.padEnd(12), hasApiKey ? ' ✓' : ' ✗', isCurrent ? ' (current)' : ''] }) }, provider));
                        }), _jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", dimColor: true, children: "\u2713 = API key configured, \u2717 = API key missing" }) })] })) : (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "blue", bold: true, marginBottom: 1, children: ["Models for ", providers[selectedProviderIndex], ":"] }), loadingModels ? (_jsx(Text, { color: "yellow", children: "Loading models..." })) : modelError ? (_jsx(Text, { color: "red", children: modelError })) : availableModels.length === 0 ? (_jsx(Text, { color: "gray", children: "No models available" })) : (_jsxs(_Fragment, { children: [availableModels.map((model, index) => {
                                    const isSelected = index === selectedModelIndex;
                                    const isCurrent = model === currentModel;
                                    return (_jsx(Box, { marginBottom: 1, children: _jsxs(Text, { color: isSelected ? 'black' : 'white', backgroundColor: isSelected ? 'cyan' : undefined, bold: isSelected || isCurrent, children: [isSelected ? '► ' : '  ', model, isCurrent ? ' (current)' : ''] }) }, model));
                                }), _jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: [availableModels.length, " models available"] }) })] }))] })) }), _jsx(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: _jsxs(Text, { color: "gray", children: ["Current: ", currentProvider, "/", currentModel] }) })] }));
}
//# sourceMappingURL=model-overlay.js.map