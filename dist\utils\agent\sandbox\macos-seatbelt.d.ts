/**
 * macOS Seatbelt Sandbox Implementation
 *
 * Uses macOS Seatbelt for process sandboxing
 * Restricts network access, file system access, and system calls
 */
import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
/**
 * Execute command with Seatbelt sandboxing
 */
export declare function execWithSeatbelt(input: ExecInput, config: AppConfig): Promise<ExecResult>;
/**
 * Test Seatbelt functionality
 */
export declare function testSeatbelt(): Promise<{
    available: boolean;
    capabilities: string[];
    limitations: string[];
}>;
/**
 * Get Seatbelt execution environment info
 */
export declare function getSeatbeltEnvironment(): {
    platform: string;
    sandboxing: boolean;
    restrictions: string[];
};
//# sourceMappingURL=macos-seatbelt.d.ts.map