import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * Advanced Spinner Component
 *
 * Provides various spinner animations with customizable styles and text
 * Supports different spinner types and loading states
 */
import { useState, useEffect } from 'react';
import { Text } from 'ink';
const SPINNER_FRAMES = {
    dots: ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'],
    line: ['|', '/', '-', '\\'],
    pipe: ['┤', '┘', '┴', '└', '├', '┌', '┬', '┐'],
    star: ['✶', '✸', '✹', '✺', '✹', '✷'],
    arrow: ['←', '↖', '↑', '↗', '→', '↘', '↓', '↙'],
    bounce: ['⠁', '⠂', '⠄', '⠂'],
    pulse: ['●', '○', '●', '○'],
    wave: ['▁', '▃', '▄', '▅', '▆', '▇', '█', '▇', '▆', '▅', '▄', '▃'],
    clock: ['🕐', '🕑', '🕒', '🕓', '🕔', '🕕', '🕖', '🕗', '🕘', '🕙', '🕚', '🕛'],
    earth: ['🌍', '🌎', '🌏']
};
const DEFAULT_INTERVALS = {
    dots: 80,
    line: 130,
    pipe: 100,
    star: 70,
    arrow: 120,
    bounce: 180,
    pulse: 200,
    wave: 60,
    clock: 100,
    earth: 180
};
export function Spinner({ type = 'dots', text = '', color = 'cyan', interval, enabled = true }) {
    const [frameIndex, setFrameIndex] = useState(0);
    const frames = SPINNER_FRAMES[type];
    const frameInterval = interval || DEFAULT_INTERVALS[type];
    useEffect(() => {
        if (!enabled) {
            return;
        }
        const timer = setInterval(() => {
            setFrameIndex(prevIndex => (prevIndex + 1) % frames.length);
        }, frameInterval);
        return () => clearInterval(timer);
    }, [frames.length, frameInterval, enabled]);
    if (!enabled) {
        return text ? _jsx(Text, { children: text }) : null;
    }
    const currentFrame = frames[frameIndex];
    return (_jsxs(Text, { color: color, children: [currentFrame, text && ` ${text}`] }));
}
export function LoadingSpinner({ message = 'Loading...', showElapsed = false, startTime, ...spinnerProps }) {
    const [elapsed, setElapsed] = useState(0);
    useEffect(() => {
        if (!showElapsed || !startTime) {
            return;
        }
        const timer = setInterval(() => {
            setElapsed(Date.now() - startTime);
        }, 100);
        return () => clearInterval(timer);
    }, [showElapsed, startTime]);
    const formatElapsed = (ms) => {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        if (minutes > 0) {
            return `${minutes}m ${seconds % 60}s`;
        }
        else {
            return `${seconds}s`;
        }
    };
    return (_jsxs(Text, { children: [_jsx(Spinner, { ...spinnerProps }), _jsxs(Text, { children: [" ", message] }), showElapsed && elapsed > 0 && (_jsxs(Text, { color: "gray", children: [" (", formatElapsed(elapsed), ")"] }))] }));
}
export function ProgressSpinner({ steps, currentStep, type = 'dots', color = 'cyan', completedColor = 'green', enabled = true }) {
    return (_jsx(_Fragment, { children: steps.map((step, index) => {
            const isCompleted = index < currentStep;
            const isCurrent = index === currentStep;
            const isPending = index > currentStep;
            return (_jsxs(Text, { children: [isCompleted && _jsx(Text, { color: completedColor, children: "\u2713 " }), isCurrent && _jsx(Spinner, { type: type, color: color, enabled: enabled }), isPending && _jsx(Text, { color: "gray", children: "\u25CB " }), _jsxs(Text, { color: isCompleted ? completedColor : isCurrent ? color : 'gray', dimColor: isPending, children: [isCurrent ? ' ' : '', step] })] }, index));
        }) }));
}
export function StatusSpinner({ status, text = '', successText, errorText, warningText, ...spinnerProps }) {
    switch (status) {
        case 'loading':
            return _jsx(Spinner, { ...spinnerProps, text: text });
        case 'success':
            return (_jsxs(Text, { color: "green", children: ["\u2713 ", successText || text || 'Success'] }));
        case 'error':
            return (_jsxs(Text, { color: "red", children: ["\u2717 ", errorText || text || 'Error'] }));
        case 'warning':
            return (_jsxs(Text, { color: "yellow", children: ["\u26A0 ", warningText || text || 'Warning'] }));
        default:
            return _jsx(Spinner, { ...spinnerProps, text: text });
    }
}
export function TypingIndicator({ color = 'gray', enabled = true }) {
    const [dotCount, setDotCount] = useState(1);
    useEffect(() => {
        if (!enabled) {
            return;
        }
        const timer = setInterval(() => {
            setDotCount(prev => (prev % 3) + 1);
        }, 500);
        return () => clearInterval(timer);
    }, [enabled]);
    if (!enabled) {
        return null;
    }
    return (_jsxs(Text, { color: color, children: ['●'.repeat(dotCount), '○'.repeat(3 - dotCount)] }));
}
export function PulseIndicator({ color = 'cyan', character = '●', interval = 600, enabled = true }) {
    const [visible, setVisible] = useState(true);
    useEffect(() => {
        if (!enabled) {
            return;
        }
        const timer = setInterval(() => {
            setVisible(prev => !prev);
        }, interval);
        return () => clearInterval(timer);
    }, [interval, enabled]);
    if (!enabled) {
        return null;
    }
    return (_jsx(Text, { color: color, dimColor: !visible, children: character }));
}
export function CustomSpinner({ frames, text = '', color = 'cyan', interval = 100, enabled = true }) {
    const [frameIndex, setFrameIndex] = useState(0);
    useEffect(() => {
        if (!enabled || frames.length === 0) {
            return;
        }
        const timer = setInterval(() => {
            setFrameIndex(prevIndex => (prevIndex + 1) % frames.length);
        }, interval);
        return () => clearInterval(timer);
    }, [frames.length, interval, enabled]);
    if (!enabled || frames.length === 0) {
        return text ? _jsx(Text, { children: text }) : null;
    }
    const currentFrame = frames[frameIndex];
    return (_jsxs(Text, { color: color, children: [currentFrame, text && ` ${text}`] }));
}
//# sourceMappingURL=spinner.js.map