{"version": 3, "file": "approval-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/approval-overlay.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AACnD,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAmB1C,MAAM,cAAc,GAAuB;IACzC;QACE,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,cAAc;QACrB,WAAW,EAAE,0CAA0C;QACvD,OAAO,EAAE;YACP,+CAA+C;YAC/C,gCAAgC;YAChC,mCAAmC;YACnC,qCAAqC;YACrC,sCAAsC;SACvC;QACD,QAAQ,EAAE,MAAM;QAChB,WAAW,EAAE,IAAI;KAClB;IACD;QACE,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,+CAA+C;QAC5D,OAAO,EAAE;YACP,8CAA8C;YAC9C,2CAA2C;YAC3C,sCAAsC;YACtC,qCAAqC;YACrC,kCAAkC;SACnC;QACD,QAAQ,EAAE,QAAQ;QAClB,WAAW,EAAE,KAAK;KACnB;IACD;QACE,IAAI,EAAE,WAAW;QACjB,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,mDAAmD;QAChE,OAAO,EAAE;YACP,6CAA6C;YAC7C,yCAAyC;YACzC,0CAA0C;YAC1C,iCAAiC;YACjC,kCAAkC;SACnC;QACD,QAAQ,EAAE,KAAK;QACf,WAAW,EAAE,KAAK;KACnB;CACF,CAAC;AAEF,MAAM,UAAU,eAAe,CAAC,EAC9B,WAAW,EACX,YAAY,EACZ,OAAO,EACP,OAAO,EACc;IACrB,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAEtD,kDAAkD;IAClD,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,SAAS,GAAG,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC;QAC9E,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC;YACnB,gBAAgB,CAAC,SAAS,CAAC,CAAC;QAC9B,CAAC;IACH,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC;IAElB,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,gBAAgB;QAChB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,aAAa;QACb,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;YACjD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QAED,YAAY;QACZ,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;YACnD,YAAY,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAChC,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,4BAA4B;QAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC;gBAChD,gBAAgB,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;YACD,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,gBAAgB,GAAG,CAAC,QAAmC,EAAU,EAAE;QACvE,QAAQ,QAAQ,EAAE,CAAC;YACjB,KAAK,MAAM,CAAC,CAAC,OAAO,OAAO,CAAC;YAC5B,KAAK,QAAQ,CAAC,CAAC,OAAO,QAAQ,CAAC;YAC/B,KAAK,KAAK,CAAC,CAAC,OAAO,KAAK,CAAC;QAC3B,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,YAAY,GAAG,cAAc,CAAC,aAAa,CAAC,CAAC;IAEnD,OAAO,CACL,MAAC,GAAG,IACF,QAAQ,EAAC,UAAU,EACnB,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EACP,KAAK,EAAE,CAAC,EACR,MAAM,EAAE,CAAC,EACT,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,MAAM,EAClB,eAAe,EAAC,OAAO,EACvB,aAAa,EAAC,QAAQ,aAGtB,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,aACpE,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,kDAEhB,EACP,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,0BACN,WAAW,2DAChB,GACH,IACF,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,KAAK,EAAC,KAAK,YACxC,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,iCAEhC,EAEN,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;4BAClC,MAAM,UAAU,GAAG,KAAK,KAAK,aAAa,CAAC;4BAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,KAAK,WAAW,CAAC;4BAE5C,OAAO,CACL,MAAC,GAAG,IAAiB,YAAY,EAAE,CAAC,aAClC,KAAC,GAAG,IAAC,KAAK,EAAE,CAAC,YACX,MAAC,IAAI,IAAC,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EAAE,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,aACzF,KAAK,GAAG,CAAC,SACL,GACH,EACN,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,aACrC,KAAC,GAAG,cACF,MAAC,IAAI,IACH,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EACrC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAChD,IAAI,EAAE,UAAU,IAAI,SAAS,aAE5B,IAAI,CAAC,KAAK,EACV,SAAS,IAAI,YAAY,EACzB,IAAI,CAAC,WAAW,IAAI,IAAI,IACpB,GACH,EACN,KAAC,GAAG,cACF,KAAC,IAAI,IACH,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EACpC,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,YAE/C,IAAI,CAAC,WAAW,GACZ,GACH,EACN,KAAC,GAAG,cACF,MAAC,IAAI,IACH,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAC7D,eAAe,EAAE,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,2BAErC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,IACjC,GACH,IACF,KAlCE,IAAI,CAAC,IAAI,CAmCb,CACP,CAAC;wBACJ,CAAC,CAAC,IACE,GACF,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YACxC,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,aACpC,YAAY,CAAC,KAAK,iBACd,EAEP,KAAC,IAAI,IAAC,YAAY,EAAE,CAAC,YAClB,YAAY,CAAC,WAAW,GACpB,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,0BAEhC,EAEN,YAAY,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,EAAE,CAAC,CAC3C,KAAC,IAAI,IAAa,YAAY,EAAE,CAAC,YAC9B,MAAM,IADE,KAAK,CAET,CACR,CAAC,EAEF,MAAC,GAAG,IAAC,SAAS,EAAE,CAAC,aACf,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,uCAAwB,EAC/C,KAAC,IAAI,IAAC,KAAK,EAAE,gBAAgB,CAAC,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,kBACvD,YAAY,CAAC,QAAQ,CAAC,WAAW,EAAE,GAC/B,EACN,YAAY,CAAC,WAAW,IAAI,CAC3B,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,UAAU,EAAE,CAAC,mCAE3B,CACR,IACG,EAEL,YAAY,CAAC,IAAI,KAAK,SAAS,IAAI,CAClC,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,OAAO,YACtE,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,mFAEZ,GACH,CACP,EAEA,YAAY,CAAC,IAAI,KAAK,WAAW,IAAI,CACpC,KAAC,GAAG,IAAC,SAAS,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,KAAK,YACpE,KAAC,IAAI,IAAC,KAAK,EAAC,KAAK,gFAEV,GACH,CACP,IACG,GACF,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,YACpE,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,uGAEX,GACH,IACF,CACP,CAAC;AACJ,CAAC"}