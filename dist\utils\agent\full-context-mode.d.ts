/**
 * Full-Context Mode Implementation
 *
 * Provides comprehensive context gathering and analysis for complex tasks
 * Automatically includes relevant files, documentation, and system information
 */
import type { MessageContent } from '../../types/index.js';
export interface FullContextOptions {
    includeFiles: boolean;
    includeGitInfo: boolean;
    includeSystemInfo: boolean;
    includeProjectStructure: boolean;
    maxFileSize: number;
    maxTotalSize: number;
    fileExtensions: string[];
    excludePatterns: string[];
    maxDepth: number;
}
export interface ContextAnalysis {
    projectType: string;
    technologies: string[];
    entryPoints: string[];
    configFiles: string[];
    documentation: string[];
    testFiles: string[];
    buildFiles: string[];
}
export interface FullContextResult {
    content: MessageContent[];
    analysis: ContextAnalysis;
    includedFiles: string[];
    totalSize: number;
    warnings: string[];
}
/**
 * Gather full context for a project or directory
 */
export declare function gatherFullContext(workingDir?: string, userQuery?: string, options?: Partial<FullContextOptions>): Promise<FullContextResult>;
//# sourceMappingURL=full-context-mode.d.ts.map