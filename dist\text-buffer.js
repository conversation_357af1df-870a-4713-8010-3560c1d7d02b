/**
 * Sophisticated Text Buffer System
 *
 * Provides advanced multi-line text editing capabilities with:
 * - Undo/Redo system
 * - Viewport management
 * - Unicode support
 * - Cursor positioning
 * - Text manipulation operations
 */
export default class TextBuffer {
    lines = [""];
    cursorRow = 0;
    cursorCol = 0;
    scrollRow = 0;
    scrollCol = 0;
    version = 0;
    undoStack = [];
    redoStack = [];
    maxUndoSteps = 100;
    constructor(initialText = "") {
        if (initialText) {
            this.lines = initialText.split('\n');
            if (this.lines.length === 0) {
                this.lines = [""];
            }
        }
        this.saveUndoState();
    }
    // ============================================================================
    // Core Text Operations
    // ============================================================================
    /**
     * Get all text as a single string
     */
    getText() {
        return this.lines.join('\n');
    }
    /**
     * Set all text, replacing current content
     */
    setText(text) {
        this.saveUndoState();
        this.lines = text.split('\n');
        if (this.lines.length === 0) {
            this.lines = [""];
        }
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.scrollRow = 0;
        this.scrollCol = 0;
        this.version++;
    }
    /**
     * Insert text at current cursor position
     */
    insertText(text) {
        this.saveUndoState();
        const lines = text.split('\n');
        const currentLine = this.lines[this.cursorRow];
        if (lines.length === 1) {
            // Single line insertion
            const newLine = currentLine.slice(0, this.cursorCol) + text + currentLine.slice(this.cursorCol);
            this.lines[this.cursorRow] = newLine;
            this.cursorCol += text.length;
        }
        else {
            // Multi-line insertion
            const beforeCursor = currentLine.slice(0, this.cursorCol);
            const afterCursor = currentLine.slice(this.cursorCol);
            // First line
            this.lines[this.cursorRow] = beforeCursor + lines[0];
            // Middle lines
            for (let i = 1; i < lines.length - 1; i++) {
                this.lines.splice(this.cursorRow + i, 0, lines[i]);
            }
            // Last line
            const lastLineIndex = this.cursorRow + lines.length - 1;
            this.lines.splice(lastLineIndex, 0, lines[lines.length - 1] + afterCursor);
            // Update cursor position
            this.cursorRow = lastLineIndex;
            this.cursorCol = lines[lines.length - 1].length;
        }
        this.version++;
    }
    /**
     * Delete character at cursor position
     */
    deleteChar() {
        if (this.cursorCol === 0 && this.cursorRow === 0) {
            return; // Nothing to delete
        }
        this.saveUndoState();
        if (this.cursorCol > 0) {
            // Delete character before cursor
            const line = this.lines[this.cursorRow];
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol - 1) + line.slice(this.cursorCol);
            this.cursorCol--;
        }
        else {
            // Delete newline, merge with previous line
            if (this.cursorRow > 0) {
                const currentLine = this.lines[this.cursorRow];
                const prevLine = this.lines[this.cursorRow - 1];
                this.lines[this.cursorRow - 1] = prevLine + currentLine;
                this.lines.splice(this.cursorRow, 1);
                this.cursorRow--;
                this.cursorCol = prevLine.length;
            }
        }
        this.version++;
    }
    /**
     * Delete character after cursor position
     */
    deleteCharForward() {
        const line = this.lines[this.cursorRow];
        if (this.cursorCol < line.length) {
            // Delete character after cursor
            this.saveUndoState();
            this.lines[this.cursorRow] = line.slice(0, this.cursorCol) + line.slice(this.cursorCol + 1);
            this.version++;
        }
        else if (this.cursorRow < this.lines.length - 1) {
            // Delete newline, merge with next line
            this.saveUndoState();
            const nextLine = this.lines[this.cursorRow + 1];
            this.lines[this.cursorRow] = line + nextLine;
            this.lines.splice(this.cursorRow + 1, 1);
            this.version++;
        }
    }
    // ============================================================================
    // Cursor Movement
    // ============================================================================
    /**
     * Move cursor to specific position
     */
    setCursor(row, col) {
        this.cursorRow = Math.max(0, Math.min(row, this.lines.length - 1));
        const line = this.lines[this.cursorRow];
        this.cursorCol = Math.max(0, Math.min(col, line.length));
    }
    /**
     * Get current cursor position
     */
    getCursor() {
        return { row: this.cursorRow, col: this.cursorCol };
    }
    /**
     * Move cursor left
     */
    moveCursorLeft() {
        if (this.cursorCol > 0) {
            this.cursorCol--;
        }
        else if (this.cursorRow > 0) {
            this.cursorRow--;
            this.cursorCol = this.lines[this.cursorRow].length;
        }
    }
    /**
     * Move cursor right
     */
    moveCursorRight() {
        const line = this.lines[this.cursorRow];
        if (this.cursorCol < line.length) {
            this.cursorCol++;
        }
        else if (this.cursorRow < this.lines.length - 1) {
            this.cursorRow++;
            this.cursorCol = 0;
        }
    }
    /**
     * Move cursor up
     */
    moveCursorUp() {
        if (this.cursorRow > 0) {
            this.cursorRow--;
            const line = this.lines[this.cursorRow];
            this.cursorCol = Math.min(this.cursorCol, line.length);
        }
    }
    /**
     * Move cursor down
     */
    moveCursorDown() {
        if (this.cursorRow < this.lines.length - 1) {
            this.cursorRow++;
            const line = this.lines[this.cursorRow];
            this.cursorCol = Math.min(this.cursorCol, line.length);
        }
    }
    /**
     * Move cursor to beginning of line
     */
    moveCursorToLineStart() {
        this.cursorCol = 0;
    }
    /**
     * Move cursor to end of line
     */
    moveCursorToLineEnd() {
        this.cursorCol = this.lines[this.cursorRow].length;
    }
    /**
     * Move cursor to beginning of buffer
     */
    moveCursorToStart() {
        this.cursorRow = 0;
        this.cursorCol = 0;
    }
    /**
     * Move cursor to end of buffer
     */
    moveCursorToEnd() {
        this.cursorRow = this.lines.length - 1;
        this.cursorCol = this.lines[this.cursorRow].length;
    }
    // ============================================================================
    // Word Operations
    // ============================================================================
    /**
     * Move cursor to next word
     */
    moveCursorToNextWord() {
        const line = this.lines[this.cursorRow];
        let col = this.cursorCol;
        // Skip current word
        while (col < line.length && /\w/.test(line[col])) {
            col++;
        }
        // Skip whitespace
        while (col < line.length && /\s/.test(line[col])) {
            col++;
        }
        if (col >= line.length && this.cursorRow < this.lines.length - 1) {
            // Move to next line
            this.cursorRow++;
            this.cursorCol = 0;
        }
        else {
            this.cursorCol = col;
        }
    }
    /**
     * Move cursor to previous word
     */
    moveCursorToPrevWord() {
        let col = this.cursorCol;
        const line = this.lines[this.cursorRow];
        if (col === 0 && this.cursorRow > 0) {
            // Move to end of previous line
            this.cursorRow--;
            this.cursorCol = this.lines[this.cursorRow].length;
            return;
        }
        // Move back one character
        col = Math.max(0, col - 1);
        // Skip whitespace
        while (col > 0 && /\s/.test(line[col])) {
            col--;
        }
        // Skip word characters
        while (col > 0 && /\w/.test(line[col - 1])) {
            col--;
        }
        this.cursorCol = col;
    }
    /**
     * Delete word before cursor
     */
    deleteWordBackward() {
        this.saveUndoState();
        const startCol = this.cursorCol;
        this.moveCursorToPrevWord();
        const endCol = this.cursorCol;
        if (this.cursorRow === this.cursorRow) {
            // Delete within same line
            const line = this.lines[this.cursorRow];
            this.lines[this.cursorRow] = line.slice(0, endCol) + line.slice(startCol);
        }
        this.version++;
    }
    // ============================================================================
    // Line Operations
    // ============================================================================
    /**
     * Insert new line at cursor
     */
    insertNewLine() {
        this.saveUndoState();
        const line = this.lines[this.cursorRow];
        const beforeCursor = line.slice(0, this.cursorCol);
        const afterCursor = line.slice(this.cursorCol);
        this.lines[this.cursorRow] = beforeCursor;
        this.lines.splice(this.cursorRow + 1, 0, afterCursor);
        this.cursorRow++;
        this.cursorCol = 0;
        this.version++;
    }
    /**
     * Delete current line
     */
    deleteLine() {
        if (this.lines.length === 1) {
            this.lines[0] = "";
            this.cursorCol = 0;
        }
        else {
            this.saveUndoState();
            this.lines.splice(this.cursorRow, 1);
            if (this.cursorRow >= this.lines.length) {
                this.cursorRow = this.lines.length - 1;
            }
            this.cursorCol = Math.min(this.cursorCol, this.lines[this.cursorRow].length);
            this.version++;
        }
    }
    /**
     * Delete from cursor to end of line
     */
    deleteToLineEnd() {
        this.saveUndoState();
        const line = this.lines[this.cursorRow];
        this.lines[this.cursorRow] = line.slice(0, this.cursorCol);
        this.version++;
    }
    // ============================================================================
    // Undo/Redo System
    // ============================================================================
    saveUndoState() {
        const state = {
            lines: [...this.lines],
            cursorRow: this.cursorRow,
            cursorCol: this.cursorCol,
            version: this.version
        };
        this.undoStack.push(state);
        // Limit undo stack size
        if (this.undoStack.length > this.maxUndoSteps) {
            this.undoStack.shift();
        }
        // Clear redo stack when new action is performed
        this.redoStack = [];
    }
    /**
     * Undo last operation
     */
    undo() {
        if (this.undoStack.length === 0) {
            return false;
        }
        // Save current state to redo stack
        const currentState = {
            lines: [...this.lines],
            cursorRow: this.cursorRow,
            cursorCol: this.cursorCol,
            version: this.version
        };
        this.redoStack.push(currentState);
        // Restore previous state
        const prevState = this.undoStack.pop();
        this.lines = [...prevState.lines];
        this.cursorRow = prevState.cursorRow;
        this.cursorCol = prevState.cursorCol;
        this.version = prevState.version;
        return true;
    }
    /**
     * Redo last undone operation
     */
    redo() {
        if (this.redoStack.length === 0) {
            return false;
        }
        // Save current state to undo stack
        this.saveUndoState();
        // Restore next state
        const nextState = this.redoStack.pop();
        this.lines = [...nextState.lines];
        this.cursorRow = nextState.cursorRow;
        this.cursorCol = nextState.cursorCol;
        this.version = nextState.version;
        return true;
    }
    // ============================================================================
    // Viewport Management
    // ============================================================================
    /**
     * Get viewport information
     */
    getViewport() {
        return {
            scrollRow: this.scrollRow,
            scrollCol: this.scrollCol,
            visibleRows: 0, // To be set by caller
            visibleCols: 0 // To be set by caller
        };
    }
    /**
     * Ensure cursor is visible in viewport
     */
    ensureCursorVisible(viewportRows, viewportCols) {
        // Vertical scrolling
        if (this.cursorRow < this.scrollRow) {
            this.scrollRow = this.cursorRow;
        }
        else if (this.cursorRow >= this.scrollRow + viewportRows) {
            this.scrollRow = this.cursorRow - viewportRows + 1;
        }
        // Horizontal scrolling
        if (this.cursorCol < this.scrollCol) {
            this.scrollCol = this.cursorCol;
        }
        else if (this.cursorCol >= this.scrollCol + viewportCols) {
            this.scrollCol = this.cursorCol - viewportCols + 1;
        }
        // Ensure scroll position is valid
        this.scrollRow = Math.max(0, this.scrollRow);
        this.scrollCol = Math.max(0, this.scrollCol);
    }
    // ============================================================================
    // Utility Methods
    // ============================================================================
    /**
     * Get line count
     */
    getLineCount() {
        return this.lines.length;
    }
    /**
     * Get line at index
     */
    getLine(index) {
        return this.lines[index] || "";
    }
    /**
     * Get all lines
     */
    getLines() {
        return [...this.lines];
    }
    /**
     * Get buffer version (for change detection)
     */
    getVersion() {
        return this.version;
    }
    /**
     * Check if buffer is empty
     */
    isEmpty() {
        return this.lines.length === 1 && this.lines[0] === "";
    }
    /**
     * Get character count
     */
    getCharCount() {
        return this.getText().length;
    }
    /**
     * Get cursor position (alias for getCursor)
     */
    getCursorPosition() {
        return this.getCursor();
    }
    /**
     * Backspace operation (alias for deleteChar)
     */
    backspace() {
        this.deleteChar();
    }
    /**
     * Delete operation (alias for deleteCharForward)
     */
    delete() {
        this.deleteCharForward();
    }
    /**
     * Clear all content
     */
    clear() {
        this.saveUndoState();
        this.lines = [''];
        this.cursorRow = 0;
        this.cursorCol = 0;
        this.scrollRow = 0;
        this.scrollCol = 0;
        this.version++;
    }
    /**
     * Select all text (placeholder for future selection support)
     */
    selectAll() {
        // Move cursor to start
        this.moveCursorToStart();
        // TODO: Implement text selection
    }
    /**
     * Get selected text (placeholder for future selection support)
     */
    getSelectedText() {
        // TODO: Implement text selection
        return '';
    }
}
//# sourceMappingURL=text-buffer.js.map