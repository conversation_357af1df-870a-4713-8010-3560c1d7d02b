/**
 * Single-Pass Mode Implementation
 *
 * Executes tasks in a single pass without iterative refinement
 * Optimized for speed and efficiency with comprehensive planning
 */
import { AgentLoop } from './agent-loop.js';
import type { ResponseItem } from '../../types/index.js';
export interface SinglePassOptions {
    useFullContext: boolean;
    maxTokens: number;
    temperature: number;
    includeSystemPrompt: boolean;
    planningPhase: boolean;
    executionPhase: boolean;
    validationPhase: boolean;
}
export interface SinglePassResult {
    success: boolean;
    items: ResponseItem[];
    executionTime: number;
    tokensUsed: number;
    phases: {
        planning?: PhaseResult;
        execution?: PhaseResult;
        validation?: PhaseResult;
    };
    warnings: string[];
    errors: string[];
}
export interface PhaseResult {
    success: boolean;
    duration: number;
    output: string;
    tokensUsed: number;
    error?: string;
}
/**
 * Execute task in single-pass mode
 */
export declare function executeSinglePass(userInput: string, agentLoop: AgentLoop, workingDir?: string, options?: Partial<SinglePassOptions>): Promise<SinglePassResult>;
/**
 * Check if single-pass mode is suitable for the task
 */
export declare function isSinglePassSuitable(userInput: string): {
    suitable: boolean;
    reasons: string[];
    confidence: number;
};
//# sourceMappingURL=single-pass-mode.d.ts.map