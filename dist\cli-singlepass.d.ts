/**
 * Single-Pass Mode Implementation
 *
 * Executes a single AI interaction without entering interactive mode
 * Useful for scripting and automation scenarios
 */
import type { AppConfig } from './types/index.js';
export interface SinglePassConfig {
    originalPrompt: string;
    config: AppConfig;
    rootPath: string;
    fullContext?: boolean;
}
/**
 * Run single-pass AI interaction
 */
export declare function runSinglePass(options: SinglePassConfig): Promise<void>;
/**
 * Validate single-pass configuration
 */
export declare function validateSinglePassConfig(config: SinglePassConfig): void;
/**
 * Get single-pass mode help
 */
export declare function getSinglePassHelp(): string;
//# sourceMappingURL=cli-singlepass.d.ts.map