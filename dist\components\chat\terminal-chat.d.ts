/**
 * Terminal Chat Interface
 *
 * Main chat component providing real-time AI interaction
 * Handles model switching, approval workflows, and conversation management
 */
import type { AppConfig } from '../../types/index.js';
interface TerminalChatProps {
    config: AppConfig;
    onExit: () => void;
}
export declare function TerminalChat({ config, onExit }: TerminalChatProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=terminal-chat.d.ts.map