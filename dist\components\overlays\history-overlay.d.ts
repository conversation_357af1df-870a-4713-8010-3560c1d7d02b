/**
 * Command History Overlay
 *
 * Browse and search through command history with keyboard navigation
 * Supports filtering, selection, and execution of previous commands
 */
interface HistoryOverlayProps {
    onSelectCommand: (command: string) => void;
    onClose: () => void;
    visible: boolean;
}
export declare function HistoryOverlay({ onSelectCommand, onClose, visible }: HistoryOverlayProps): import("react/jsx-runtime").JSX.Element | null;
export {};
//# sourceMappingURL=history-overlay.d.ts.map