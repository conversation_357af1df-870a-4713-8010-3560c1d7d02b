/**
 * Command History Browser Component
 *
 * Interactive browser for command history with search and filtering
 * Supports command re-execution and history management
 */
interface HistoryBrowserProps {
    onSelect: (command: string) => void;
    onCancel: () => void;
}
export declare function HistoryBrowser({ onSelect, onCancel }: HistoryBrowserProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=history-browser.d.ts.map