/**
 * Model Information System
 *
 * Comprehensive model metadata including context lengths, capabilities,
 * and support for latest models (o1, o3, GPT-4.1, etc.)
 */
import type { ModelInfo, ProviderName } from '../types/index.js';
/**
 * Comprehensive model database with detailed information
 */
export declare const MODEL_DATABASE: Record<string, ModelInfo>;
/**
 * Get model information by ID
 */
export declare function getModelInfo(modelId: string): ModelInfo | null;
/**
 * Get all models for a specific provider
 */
export declare function getModelsForProvider(provider: ProviderName): ModelInfo[];
/**
 * Get models that support images
 */
export declare function getImageSupportedModels(): ModelInfo[];
/**
 * Get models that support tools/function calling
 */
export declare function getToolSupportedModels(): ModelInfo[];
/**
 * Get models with high context length (>= 32K tokens)
 */
export declare function getHighContextModels(): ModelInfo[];
/**
 * Search models by name or ID
 */
export declare function searchModels(query: string): ModelInfo[];
/**
 * Get model capabilities summary
 */
export declare function getModelCapabilities(modelId: string): {
    hasImages: boolean;
    hasTools: boolean;
    contextSize: 'small' | 'medium' | 'large' | 'xlarge';
    provider: string;
} | null;
//# sourceMappingURL=model-info.d.ts.map