/**
 * Telemetry System
 *
 * Collects usage metrics, performance data, and error tracking
 * Provides insights for improving user experience and system performance
 */
export interface TelemetryEvent {
    id: string;
    type: TelemetryEventType;
    timestamp: number;
    sessionId: string;
    userId?: string;
    data: Record<string, any>;
    metadata: {
        version: string;
        platform: string;
        arch: string;
        nodeVersion: string;
    };
}
export type TelemetryEventType = 'app_start' | 'app_exit' | 'command_executed' | 'model_changed' | 'provider_changed' | 'error_occurred' | 'performance_metric' | 'feature_used' | 'session_created' | 'session_loaded' | 'file_processed' | 'tool_called' | 'approval_requested' | 'approval_granted' | 'approval_denied';
export interface TelemetryConfig {
    enabled: boolean;
    endpoint?: string;
    apiKey?: string;
    batchSize: number;
    flushInterval: number;
    retryAttempts: number;
    anonymize: boolean;
    collectPerformance: boolean;
    collectErrors: boolean;
    collectUsage: boolean;
}
export interface PerformanceMetric {
    name: string;
    value: number;
    unit: string;
    timestamp: number;
    context?: Record<string, any>;
}
export interface UsageStats {
    totalCommands: number;
    totalSessions: number;
    totalErrors: number;
    averageSessionDuration: number;
    mostUsedProvider: string;
    mostUsedModel: string;
    featuresUsed: string[];
    lastActivity: number;
}
declare class TelemetryManager {
    private config;
    private sessionId;
    private userId?;
    private events;
    private flushTimer?;
    private telemetryDir;
    private sessionStartTime;
    constructor(config?: Partial<TelemetryConfig>);
    /**
     * Initialize telemetry system
     */
    private initializeTelemetry;
    /**
     * Track an event
     */
    trackEvent(type: TelemetryEventType, data?: Record<string, any>): void;
    /**
     * Track performance metric
     */
    trackPerformance(metric: PerformanceMetric): void;
    /**
     * Track error
     */
    trackError(error: Error, context?: Record<string, any>): void;
    /**
     * Track command execution
     */
    trackCommand(command: string, success: boolean, duration: number, context?: Record<string, any>): void;
    /**
     * Track feature usage
     */
    trackFeature(feature: string, data?: Record<string, any>): void;
    /**
     * Get usage statistics
     */
    getUsageStats(): UsageStats;
    /**
     * Update usage statistics
     */
    updateUsageStats(updates: Partial<UsageStats>): void;
    /**
     * Flush events to storage/endpoint
     */
    flush(): Promise<void>;
    /**
     * Shutdown telemetry
     */
    shutdown(): Promise<void>;
    /**
     * Generate session ID
     */
    private generateSessionId;
    /**
     * Generate event ID
     */
    private generateEventId;
    /**
     * Load or generate user ID
     */
    private loadUserId;
    /**
     * Hash user ID for anonymization
     */
    private hashUserId;
    /**
     * Sanitize data to remove sensitive information
     */
    private sanitizeData;
    /**
     * Sanitize command to remove sensitive information
     */
    private sanitizeCommand;
    /**
     * Start flush timer
     */
    private startFlushTimer;
    /**
     * Save events to local storage
     */
    private saveEventsLocally;
    /**
     * Send events to endpoint
     */
    private sendEventsToEndpoint;
}
/**
 * Initialize telemetry
 */
export declare function initializeTelemetry(config?: Partial<TelemetryConfig>): void;
/**
 * Get telemetry manager instance
 */
export declare function getTelemetry(): TelemetryManager | null;
/**
 * Track event (convenience function)
 */
export declare function trackEvent(type: TelemetryEventType, data?: Record<string, any>): void;
/**
 * Track performance (convenience function)
 */
export declare function trackPerformance(metric: PerformanceMetric): void;
/**
 * Track error (convenience function)
 */
export declare function trackError(error: Error, context?: Record<string, any>): void;
/**
 * Track command (convenience function)
 */
export declare function trackCommand(command: string, success: boolean, duration: number, context?: Record<string, any>): void;
/**
 * Track feature usage (convenience function)
 */
export declare function trackFeature(feature: string, data?: Record<string, any>): void;
/**
 * Shutdown telemetry (convenience function)
 */
export declare function shutdownTelemetry(): Promise<void>;
export {};
//# sourceMappingURL=telemetry.d.ts.map