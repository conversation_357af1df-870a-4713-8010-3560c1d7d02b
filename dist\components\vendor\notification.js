import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * Notification System
 *
 * Provides toast-style notifications with different types and animations
 * Supports auto-dismiss, actions, and persistent notifications
 */
import { useState, useEffect, useCallback } from 'react';
import { Box, Text } from 'ink';
/**
 * Single notification component
 */
export function Notification({ notification, onDismiss, onAction }) {
    const [timeLeft, setTimeLeft] = useState(notification.duration || 0);
    useEffect(() => {
        if (notification.duration && notification.duration > 0) {
            const startTime = Date.now();
            const endTime = notification.timestamp + notification.duration;
            const timer = setInterval(() => {
                const now = Date.now();
                const remaining = Math.max(0, endTime - now);
                setTimeLeft(remaining);
                if (remaining === 0) {
                    onDismiss(notification.id);
                }
            }, 100);
            return () => clearInterval(timer);
        }
    }, [notification.id, notification.duration, notification.timestamp, onDismiss]);
    const getIcon = () => {
        switch (notification.type) {
            case 'success': return '✓';
            case 'error': return '✗';
            case 'warning': return '⚠';
            case 'info': return 'ℹ';
            default: return '•';
        }
    };
    const getColor = () => {
        switch (notification.type) {
            case 'success': return 'green';
            case 'error': return 'red';
            case 'warning': return 'yellow';
            case 'info': return 'blue';
            default: return 'white';
        }
    };
    const formatTimeLeft = (ms) => {
        const seconds = Math.ceil(ms / 1000);
        return `${seconds}s`;
    };
    return (_jsxs(Box, { borderStyle: "round", borderColor: getColor(), paddingX: 1, paddingY: 0, marginBottom: 1, flexDirection: "column", children: [_jsxs(Box, { justifyContent: "space-between", children: [_jsx(Box, { children: _jsxs(Text, { color: getColor(), bold: true, children: [getIcon(), " ", notification.title] }) }), _jsxs(Box, { children: [notification.duration && notification.duration > 0 && timeLeft > 0 && (_jsx(Text, { color: "gray", dimColor: true, children: formatTimeLeft(timeLeft) })), _jsx(Text, { color: "gray", dimColor: true, marginLeft: 1, children: "[ESC]" })] })] }), notification.message && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { children: notification.message }) })), notification.actions && notification.actions.length > 0 && (_jsx(Box, { marginTop: 1, gap: 1, children: notification.actions.map((action, index) => (_jsxs(Text, { color: "cyan", children: ["[", action.key, "] ", action.label] }, action.key))) }))] }));
}
/**
 * Notification manager component
 */
export function NotificationManager({ notifications, maxVisible = 5, position = 'top', onDismiss, onAction }) {
    // Sort notifications by timestamp (newest first for top, oldest first for bottom)
    const sortedNotifications = [...notifications].sort((a, b) => {
        return position === 'top' ? b.timestamp - a.timestamp : a.timestamp - b.timestamp;
    });
    // Limit visible notifications
    const visibleNotifications = sortedNotifications.slice(0, maxVisible);
    if (visibleNotifications.length === 0) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", top: position === 'top' ? 1 : undefined, bottom: position === 'bottom' ? 1 : undefined, right: 1, width: 60, flexDirection: "column", children: [visibleNotifications.map(notification => (_jsx(Notification, { notification: notification, onDismiss: onDismiss, onAction: onAction }, notification.id))), notifications.length > maxVisible && (_jsx(Box, { paddingX: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["+", notifications.length - maxVisible, " more notifications"] }) }))] }));
}
/**
 * Notification hook for managing notifications
 */
export function useNotifications() {
    const [notifications, setNotifications] = useState([]);
    const addNotification = useCallback((type, title, message, options) => {
        const notification = {
            id: options?.id || `notification-${Date.now()}-${Math.random()}`,
            type,
            title,
            message,
            duration: options?.duration ?? (type === 'error' ? 0 : 5000), // Errors persist by default
            actions: options?.actions,
            timestamp: Date.now()
        };
        setNotifications(prev => [...prev, notification]);
        return notification.id;
    }, []);
    const dismissNotification = useCallback((id) => {
        setNotifications(prev => prev.filter(n => n.id !== id));
    }, []);
    const handleAction = useCallback((id, actionKey) => {
        const notification = notifications.find(n => n.id === id);
        if (notification) {
            const action = notification.actions?.find(a => a.key === actionKey);
            if (action) {
                action.action();
                // Optionally dismiss notification after action
                dismissNotification(id);
            }
        }
    }, [notifications, dismissNotification]);
    const clearAll = useCallback(() => {
        setNotifications([]);
    }, []);
    // Convenience methods
    const showInfo = useCallback((title, message, options) => {
        return addNotification('info', title, message, options);
    }, [addNotification]);
    const showSuccess = useCallback((title, message, options) => {
        return addNotification('success', title, message, options);
    }, [addNotification]);
    const showWarning = useCallback((title, message, options) => {
        return addNotification('warning', title, message, options);
    }, [addNotification]);
    const showError = useCallback((title, message, options) => {
        return addNotification('error', title, message, options);
    }, [addNotification]);
    return {
        notifications,
        addNotification,
        dismissNotification,
        handleAction,
        clearAll,
        showInfo,
        showSuccess,
        showWarning,
        showError
    };
}
export function Toast({ type, message, visible, onDismiss }) {
    useEffect(() => {
        if (visible && onDismiss) {
            const timer = setTimeout(onDismiss, 3000);
            return () => clearTimeout(timer);
        }
    }, [visible, onDismiss]);
    if (!visible) {
        return null;
    }
    const getIcon = () => {
        switch (type) {
            case 'success': return '✓';
            case 'error': return '✗';
            case 'warning': return '⚠';
            case 'info': return 'ℹ';
            default: return '•';
        }
    };
    const getColor = () => {
        switch (type) {
            case 'success': return 'green';
            case 'error': return 'red';
            case 'warning': return 'yellow';
            case 'info': return 'blue';
            default: return 'white';
        }
    };
    return (_jsx(Box, { position: "absolute", top: 1, right: 1, borderStyle: "round", borderColor: getColor(), paddingX: 2, paddingY: 1, backgroundColor: "black", children: _jsxs(Text, { color: getColor(), children: [getIcon(), " ", message] }) }));
}
export function ProgressNotification({ title, progress, message, visible }) {
    if (!visible) {
        return null;
    }
    const progressBar = '█'.repeat(Math.floor(progress / 5)) +
        '░'.repeat(20 - Math.floor(progress / 5));
    return (_jsxs(Box, { position: "absolute", top: 1, right: 1, borderStyle: "round", borderColor: "blue", paddingX: 2, paddingY: 1, backgroundColor: "black", flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, children: title }), _jsx(Box, { marginTop: 1, children: _jsxs(Text, { color: "cyan", children: ["[", progressBar, "] ", Math.round(progress), "%"] }) }), message && (_jsx(Box, { marginTop: 1, children: _jsx(Text, { color: "gray", children: message }) }))] }));
}
//# sourceMappingURL=notification.js.map