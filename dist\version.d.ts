/**
 * Version Management System
 *
 * Provides dynamic version loading from package.json
 * Ensures version consistency across the application
 */
declare let CLI_VERSION: string;
export { CLI_VERSION };
/**
 * Get the current CLI version
 */
export declare function getVersion(): string;
/**
 * Get version information for display
 */
export declare function getVersionInfo(): {
    version: string;
    node: string;
    platform: string;
    arch: string;
};
//# sourceMappingURL=version.d.ts.map