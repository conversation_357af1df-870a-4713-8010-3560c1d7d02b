/**
 * Telemetry System
 *
 * Collects usage metrics, performance data, and error tracking
 * Provides insights for improving user experience and system performance
 */
import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { tmpdir, platform, arch } from 'os';
import { CLI_VERSION } from '../version.js';
import { logInfo, logError } from './logger/log.js';
class TelemetryManager {
    config;
    sessionId;
    userId;
    events = [];
    flushTimer;
    telemetryDir;
    sessionStartTime;
    constructor(config = {}) {
        this.config = {
            enabled: true,
            batchSize: 50,
            flushInterval: 30000, // 30 seconds
            retryAttempts: 3,
            anonymize: true,
            collectPerformance: true,
            collectErrors: true,
            collectUsage: true,
            ...config
        };
        this.sessionId = this.generateSessionId();
        this.sessionStartTime = Date.now();
        this.telemetryDir = join(tmpdir(), 'kritrima-telemetry');
        this.initializeTelemetry();
    }
    /**
     * Initialize telemetry system
     */
    initializeTelemetry() {
        if (!this.config.enabled) {
            return;
        }
        try {
            // Create telemetry directory
            if (!existsSync(this.telemetryDir)) {
                mkdirSync(this.telemetryDir, { recursive: true });
            }
            // Load or generate user ID
            this.loadUserId();
            // Start flush timer
            this.startFlushTimer();
            // Track app start
            this.trackEvent('app_start', {
                version: CLI_VERSION,
                sessionId: this.sessionId
            });
            logInfo(`Telemetry initialized - Session: ${this.sessionId}, Enabled: ${this.config.enabled}`);
        }
        catch (error) {
            logError('Failed to initialize telemetry', error instanceof Error ? error : new Error(String(error)));
        }
    }
    /**
     * Track an event
     */
    trackEvent(type, data = {}) {
        if (!this.config.enabled) {
            return;
        }
        try {
            const event = {
                id: this.generateEventId(),
                type,
                timestamp: Date.now(),
                sessionId: this.sessionId,
                userId: this.config.anonymize ? this.hashUserId(this.userId) : this.userId,
                data: this.sanitizeData(data),
                metadata: {
                    version: CLI_VERSION,
                    platform: platform(),
                    arch: arch(),
                    nodeVersion: process.version
                }
            };
            this.events.push(event);
            // Auto-flush if batch size reached
            if (this.events.length >= this.config.batchSize) {
                this.flush();
            }
        }
        catch (error) {
            logError('Failed to track event', error instanceof Error ? error : new Error(String(error)));
        }
    }
    /**
     * Track performance metric
     */
    trackPerformance(metric) {
        if (!this.config.enabled || !this.config.collectPerformance) {
            return;
        }
        this.trackEvent('performance_metric', {
            metric: metric.name,
            value: metric.value,
            unit: metric.unit,
            context: metric.context
        });
    }
    /**
     * Track error
     */
    trackError(error, context = {}) {
        if (!this.config.enabled || !this.config.collectErrors) {
            return;
        }
        this.trackEvent('error_occurred', {
            message: error.message,
            stack: error.stack,
            name: error.name,
            context
        });
    }
    /**
     * Track command execution
     */
    trackCommand(command, success, duration, context = {}) {
        if (!this.config.enabled || !this.config.collectUsage) {
            return;
        }
        this.trackEvent('command_executed', {
            command: this.sanitizeCommand(command),
            success,
            duration,
            ...context
        });
    }
    /**
     * Track feature usage
     */
    trackFeature(feature, data = {}) {
        if (!this.config.enabled || !this.config.collectUsage) {
            return;
        }
        this.trackEvent('feature_used', {
            feature,
            ...data
        });
    }
    /**
     * Get usage statistics
     */
    getUsageStats() {
        try {
            const statsFile = join(this.telemetryDir, 'usage-stats.json');
            if (existsSync(statsFile)) {
                const stats = JSON.parse(readFileSync(statsFile, 'utf-8'));
                return stats;
            }
        }
        catch (error) {
            // Return default stats on error
        }
        return {
            totalCommands: 0,
            totalSessions: 0,
            totalErrors: 0,
            averageSessionDuration: 0,
            mostUsedProvider: 'openai',
            mostUsedModel: 'gpt-4o',
            featuresUsed: [],
            lastActivity: Date.now()
        };
    }
    /**
     * Update usage statistics
     */
    updateUsageStats(updates) {
        try {
            const stats = this.getUsageStats();
            const updatedStats = { ...stats, ...updates, lastActivity: Date.now() };
            const statsFile = join(this.telemetryDir, 'usage-stats.json');
            writeFileSync(statsFile, JSON.stringify(updatedStats, null, 2));
        }
        catch (error) {
            logError('Failed to update usage stats', error instanceof Error ? error : new Error(String(error)));
        }
    }
    /**
     * Flush events to storage/endpoint
     */
    async flush() {
        if (this.events.length === 0) {
            return;
        }
        try {
            const eventsToFlush = [...this.events];
            this.events = [];
            // Save to local storage
            await this.saveEventsLocally(eventsToFlush);
            // Send to endpoint if configured
            if (this.config.endpoint) {
                await this.sendEventsToEndpoint(eventsToFlush);
            }
            logInfo(`Telemetry events flushed - Count: ${eventsToFlush.length}`);
        }
        catch (error) {
            logError('Failed to flush telemetry events', error instanceof Error ? error : new Error(String(error)));
            // Re-add events to queue for retry
            this.events.unshift(...this.events);
        }
    }
    /**
     * Shutdown telemetry
     */
    async shutdown() {
        try {
            // Track app exit
            this.trackEvent('app_exit', {
                sessionDuration: Date.now() - this.sessionStartTime
            });
            // Stop flush timer
            if (this.flushTimer) {
                clearInterval(this.flushTimer);
            }
            // Final flush
            await this.flush();
            logInfo('Telemetry shutdown completed');
        }
        catch (error) {
            logError('Failed to shutdown telemetry', error instanceof Error ? error : new Error(String(error)));
        }
    }
    /**
     * Generate session ID
     */
    generateSessionId() {
        return `session-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    }
    /**
     * Generate event ID
     */
    generateEventId() {
        return `event-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
    }
    /**
     * Load or generate user ID
     */
    loadUserId() {
        try {
            const userIdFile = join(this.telemetryDir, 'user-id.txt');
            if (existsSync(userIdFile)) {
                this.userId = readFileSync(userIdFile, 'utf-8').trim();
            }
            else {
                this.userId = `user-${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
                writeFileSync(userIdFile, this.userId);
            }
        }
        catch (error) {
            this.userId = `temp-${Math.random().toString(36).substring(2, 15)}`;
        }
    }
    /**
     * Hash user ID for anonymization
     */
    hashUserId(userId) {
        if (!userId)
            return undefined;
        // Simple hash for anonymization
        let hash = 0;
        for (let i = 0; i < userId.length; i++) {
            const char = userId.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32-bit integer
        }
        return `anon-${Math.abs(hash).toString(36)}`;
    }
    /**
     * Sanitize data to remove sensitive information
     */
    sanitizeData(data) {
        const sanitized = { ...data };
        const sensitiveKeys = ['password', 'token', 'key', 'secret', 'auth'];
        for (const key in sanitized) {
            if (sensitiveKeys.some(sensitive => key.toLowerCase().includes(sensitive))) {
                sanitized[key] = '[REDACTED]';
            }
        }
        return sanitized;
    }
    /**
     * Sanitize command to remove sensitive information
     */
    sanitizeCommand(command) {
        // Remove potential sensitive information from commands
        return command
            .replace(/--password[=\s]+\S+/gi, '--password=[REDACTED]')
            .replace(/--token[=\s]+\S+/gi, '--token=[REDACTED]')
            .replace(/--key[=\s]+\S+/gi, '--key=[REDACTED]')
            .replace(/--secret[=\s]+\S+/gi, '--secret=[REDACTED]');
    }
    /**
     * Start flush timer
     */
    startFlushTimer() {
        this.flushTimer = setInterval(() => {
            this.flush();
        }, this.config.flushInterval);
    }
    /**
     * Save events to local storage
     */
    async saveEventsLocally(events) {
        try {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const filename = `events-${timestamp}.json`;
            const filepath = join(this.telemetryDir, filename);
            writeFileSync(filepath, JSON.stringify(events, null, 2));
        }
        catch (error) {
            throw new Error(`Failed to save events locally: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    /**
     * Send events to endpoint
     */
    async sendEventsToEndpoint(events) {
        if (!this.config.endpoint) {
            return;
        }
        try {
            const response = await fetch(this.config.endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
                },
                body: JSON.stringify({
                    events,
                    metadata: {
                        version: CLI_VERSION,
                        timestamp: Date.now()
                    }
                })
            });
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
        }
        catch (error) {
            throw new Error(`Failed to send events to endpoint: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}
// Global telemetry instance
let telemetryManager = null;
/**
 * Initialize telemetry
 */
export function initializeTelemetry(config = {}) {
    if (!telemetryManager) {
        telemetryManager = new TelemetryManager(config);
    }
}
/**
 * Get telemetry manager instance
 */
export function getTelemetry() {
    return telemetryManager;
}
/**
 * Track event (convenience function)
 */
export function trackEvent(type, data = {}) {
    telemetryManager?.trackEvent(type, data);
}
/**
 * Track performance (convenience function)
 */
export function trackPerformance(metric) {
    telemetryManager?.trackPerformance(metric);
}
/**
 * Track error (convenience function)
 */
export function trackError(error, context = {}) {
    telemetryManager?.trackError(error, context);
}
/**
 * Track command (convenience function)
 */
export function trackCommand(command, success, duration, context = {}) {
    telemetryManager?.trackCommand(command, success, duration, context);
}
/**
 * Track feature usage (convenience function)
 */
export function trackFeature(feature, data = {}) {
    telemetryManager?.trackFeature(feature, data);
}
/**
 * Shutdown telemetry (convenience function)
 */
export async function shutdownTelemetry() {
    if (telemetryManager) {
        await telemetryManager.shutdown();
        telemetryManager = null;
    }
}
//# sourceMappingURL=telemetry.js.map