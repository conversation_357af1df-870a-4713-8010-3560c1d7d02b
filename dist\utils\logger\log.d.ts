/**
 * Comprehensive Logging System
 *
 * Multi-platform logging with async queue processing
 * Supports conditional logging and performance monitoring
 */
export interface Logger {
    log(message: string, details?: any): void;
    error(message: string, error?: Error): void;
    warn(message: string): void;
    info(message: string, details?: any): void;
    debug(message: string): void;
}
/**
 * Check if logging is enabled
 */
export declare function isLoggingEnabled(): boolean;
/**
 * Get the global logger instance
 */
export declare function getLogger(): Logger;
/**
 * Log a message
 */
export declare function log(message: string): void;
/**
 * Log an error
 */
export declare function logError(message: string, error?: Error): void;
/**
 * Log a warning
 */
export declare function logWarn(message: string): void;
/**
 * Log an info message
 */
export declare function logInfo(message: string): void;
/**
 * Log a debug message
 */
export declare function logDebug(message: string): void;
/**
 * Performance monitoring for UI rendering
 */
export declare class PerformanceMonitor {
    private frameCount;
    private lastTime;
    private fpsHistory;
    logFrame(): void;
    getAverageFPS(): number;
}
export declare const performanceMonitor: PerformanceMonitor;
/**
 * Log agent loop execution
 */
export declare function logAgentExecution(action: string, details: any, duration?: number): void;
/**
 * Log network requests
 */
export declare function logNetworkRequest(method: string, url: string, status?: number, duration?: number): void;
/**
 * Get recent log entries for bug reports
 */
export declare function getRecentLogs(count?: number): string[];
//# sourceMappingURL=log.d.ts.map