import { jsxs as _jsxs, jsx as _jsx } from "react/jsx-runtime";
/**
 * Help Information Overlay
 *
 * Comprehensive help system with keyboard shortcuts, commands, and usage tips
 * Provides contextual help and feature explanations
 */
import { useState } from 'react';
import { Box, Text, useInput } from 'ink';
import { SLASH_COMMANDS } from '../../utils/slash-commands.js';
import { CLI_VERSION } from '../../version.js';
const HELP_SECTIONS = [
    {
        id: 'overview',
        title: 'Overview',
        description: 'Introduction and basic usage'
    },
    {
        id: 'commands',
        title: 'Slash Commands',
        description: 'Built-in commands and their usage'
    },
    {
        id: 'shortcuts',
        title: 'Keyboard Shortcuts',
        description: 'Navigation and productivity shortcuts'
    },
    {
        id: 'features',
        title: 'Features',
        description: 'Advanced features and capabilities'
    },
    {
        id: 'tips',
        title: 'Tips & Tricks',
        description: 'Best practices and productivity tips'
    }
];
export function HelpOverlay({ onClose, visible }) {
    const [selectedSection, setSelectedSection] = useState('overview');
    // Handle keyboard input
    useInput((input, key) => {
        if (!visible)
            return;
        // Close overlay
        if (key.escape) {
            onClose();
            return;
        }
        // Section navigation
        if (key.tab) {
            const currentIndex = HELP_SECTIONS.findIndex(s => s.id === selectedSection);
            const nextIndex = (currentIndex + 1) % HELP_SECTIONS.length;
            setSelectedSection(HELP_SECTIONS[nextIndex].id);
            return;
        }
        // Quick section selection
        if (input >= '1' && input <= '5') {
            const index = parseInt(input) - 1;
            if (index >= 0 && index < HELP_SECTIONS.length) {
                setSelectedSection(HELP_SECTIONS[index].id);
            }
            return;
        }
    });
    /**
     * Render overview section
     */
    const renderOverview = () => (_jsxs(Box, { flexDirection: "column", children: [_jsxs(Text, { color: "blue", bold: true, marginBottom: 1, children: ["Welcome to Kritrima AI CLI v", CLI_VERSION] }), _jsx(Text, { marginBottom: 1, children: "A sophisticated AI-powered CLI assistant with multi-provider support, autonomous agent capabilities, and advanced code assistance." }), _jsx(Text, { color: "blue", bold: true, marginTop: 1, marginBottom: 1, children: "Getting Started:" }), _jsx(Text, { children: "\u2022 Type your questions or requests in natural language" }), _jsx(Text, { children: "\u2022 Use @filename to include files in your context" }), _jsx(Text, { children: "\u2022 Start commands with / for built-in functions" }), _jsx(Text, { children: "\u2022 Press Tab for auto-completion" }), _jsx(Text, { children: "\u2022 Use arrow keys to navigate command history" }), _jsx(Text, { color: "blue", bold: true, marginTop: 2, marginBottom: 1, children: "Key Features:" }), _jsx(Text, { children: "\u2022 Multi-provider AI support (OpenAI, Gemini, Ollama, etc.)" }), _jsx(Text, { children: "\u2022 Autonomous tool calling and command execution" }), _jsx(Text, { children: "\u2022 Advanced file operations and code assistance" }), _jsx(Text, { children: "\u2022 Session management and conversation history" }), _jsx(Text, { children: "\u2022 Configurable approval modes for security" }), _jsx(Text, { children: "\u2022 Real-time streaming responses" })] }));
    /**
     * Render commands section
     */
    const renderCommands = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, marginBottom: 1, children: "Available Slash Commands:" }), SLASH_COMMANDS.map((cmd, index) => (_jsxs(Box, { marginBottom: 1, children: [_jsx(Box, { width: 15, children: _jsx(Text, { color: "cyan", bold: true, children: cmd.command }) }), _jsx(Box, { flexGrow: 1, children: _jsx(Text, { children: cmd.description }) })] }, cmd.command))), _jsx(Text, { color: "blue", bold: true, marginTop: 2, marginBottom: 1, children: "Command Usage:" }), _jsx(Text, { children: "\u2022 Commands start with / (e.g., /help, /model)" }), _jsx(Text, { children: "\u2022 Some commands accept parameters (e.g., /model set gpt-4)" }), _jsx(Text, { children: "\u2022 Use Tab for command auto-completion" }), _jsx(Text, { children: "\u2022 Type /help <command> for detailed help" })] }));
    /**
     * Render shortcuts section
     */
    const renderShortcuts = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, marginBottom: 1, children: "Keyboard Shortcuts:" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 1, children: "Navigation:" }), _jsx(Text, { children: "\u2022 \u2191/\u2193 Arrow Keys    - Navigate command history" }), _jsx(Text, { children: "\u2022 Tab              - Auto-complete commands/files" }), _jsx(Text, { children: "\u2022 Escape           - Close overlays/clear input" }), _jsx(Text, { children: "\u2022 Page Up/Down     - Scroll through lists" }), _jsx(Text, { children: "\u2022 Home/End         - Jump to start/end of lists" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Input & Editing:" }), _jsx(Text, { children: "\u2022 Enter            - Submit command/message" }), _jsx(Text, { children: "\u2022 Shift+Enter      - New line (multiline mode)" }), _jsx(Text, { children: "\u2022 Ctrl+A           - Select all text" }), _jsx(Text, { children: "\u2022 Ctrl+C           - Copy selected text" }), _jsx(Text, { children: "\u2022 Ctrl+V           - Paste text" }), _jsx(Text, { children: "\u2022 Ctrl+Z           - Undo" }), _jsx(Text, { children: "\u2022 Ctrl+Y           - Redo" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Application:" }), _jsx(Text, { children: "\u2022 Ctrl+C           - Exit application" }), _jsx(Text, { children: "\u2022 Ctrl+F           - Toggle search mode" }), _jsx(Text, { children: "\u2022 Ctrl+L           - Clear screen" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Overlays:" }), _jsx(Text, { children: "\u2022 /model           - Open model selection" }), _jsx(Text, { children: "\u2022 /history         - Open command history" }), _jsx(Text, { children: "\u2022 /sessions        - Open session browser" }), _jsx(Text, { children: "\u2022 /approval        - Open approval settings" }), _jsx(Text, { children: "\u2022 /help            - Open this help" })] }));
    /**
     * Render features section
     */
    const renderFeatures = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, marginBottom: 1, children: "Advanced Features:" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 1, children: "File Integration:" }), _jsx(Text, { children: "\u2022 Use @filename to include files in context" }), _jsx(Text, { children: "\u2022 Supports relative and absolute paths" }), _jsx(Text, { children: "\u2022 Auto-completion for file paths" }), _jsx(Text, { children: "\u2022 Automatic file content expansion" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "AI Providers:" }), _jsx(Text, { children: "\u2022 OpenAI (GPT-3.5, GPT-4, GPT-4 Turbo)" }), _jsx(Text, { children: "\u2022 Google Gemini (Gemini Pro, Gemini Ultra)" }), _jsx(Text, { children: "\u2022 Ollama (Local models)" }), _jsx(Text, { children: "\u2022 Mistral AI, DeepSeek, xAI, Groq" }), _jsx(Text, { children: "\u2022 Custom provider support" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Security & Approval:" }), _jsx(Text, { children: "\u2022 Suggest Mode: Manual approval for all actions" }), _jsx(Text, { children: "\u2022 Auto-Edit Mode: Automatic file edits, manual commands" }), _jsx(Text, { children: "\u2022 Full-Auto Mode: Automatic everything (sandboxed)" }), _jsx(Text, { children: "\u2022 Configurable safe command lists" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Session Management:" }), _jsx(Text, { children: "\u2022 Automatic conversation saving" }), _jsx(Text, { children: "\u2022 Session browsing and restoration" }), _jsx(Text, { children: "\u2022 Export/import capabilities" }), _jsx(Text, { children: "\u2022 Session search and filtering" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Tool Calling:" }), _jsx(Text, { children: "\u2022 Autonomous shell command execution" }), _jsx(Text, { children: "\u2022 File operations and code generation" }), _jsx(Text, { children: "\u2022 Git integration and diff viewing" }), _jsx(Text, { children: "\u2022 Package manager detection" })] }));
    /**
     * Render tips section
     */
    const renderTips = () => (_jsxs(Box, { flexDirection: "column", children: [_jsx(Text, { color: "blue", bold: true, marginBottom: 1, children: "Tips & Best Practices:" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 1, children: "Productivity Tips:" }), _jsx(Text, { children: "\u2022 Use descriptive prompts for better AI responses" }), _jsx(Text, { children: "\u2022 Include relevant files with @filename syntax" }), _jsx(Text, { children: "\u2022 Break complex tasks into smaller steps" }), _jsx(Text, { children: "\u2022 Use /compact to reduce token usage in long conversations" }), _jsx(Text, { children: "\u2022 Save important sessions for future reference" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Security Best Practices:" }), _jsx(Text, { children: "\u2022 Use 'suggest' mode in production environments" }), _jsx(Text, { children: "\u2022 Review commands before approval" }), _jsx(Text, { children: "\u2022 Avoid including sensitive data in prompts" }), _jsx(Text, { children: "\u2022 Regularly clean up old sessions" }), _jsx(Text, { children: "\u2022 Use environment variables for API keys" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Troubleshooting:" }), _jsx(Text, { children: "\u2022 Check API key configuration if models don't load" }), _jsx(Text, { children: "\u2022 Use /bug to generate bug reports" }), _jsx(Text, { children: "\u2022 Enable debug mode with --debug flag" }), _jsx(Text, { children: "\u2022 Check network connectivity for provider issues" }), _jsx(Text, { children: "\u2022 Clear history if experiencing performance issues" }), _jsx(Text, { color: "yellow", bold: true, marginTop: 2, children: "Performance Tips:" }), _jsx(Text, { children: "\u2022 Use shorter context for faster responses" }), _jsx(Text, { children: "\u2022 Choose appropriate models for your tasks" }), _jsx(Text, { children: "\u2022 Enable notifications to work on other tasks" }), _jsx(Text, { children: "\u2022 Use local models (Ollama) for privacy" }), _jsx(Text, { children: "\u2022 Regularly update to the latest version" })] }));
    /**
     * Render content based on selected section
     */
    const renderContent = () => {
        switch (selectedSection) {
            case 'overview': return renderOverview();
            case 'commands': return renderCommands();
            case 'shortcuts': return renderShortcuts();
            case 'features': return renderFeatures();
            case 'tips': return renderTips();
            default: return renderOverview();
        }
    };
    if (!visible) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", top: 0, left: 0, right: 0, bottom: 0, borderStyle: "double", borderColor: "cyan", backgroundColor: "black", flexDirection: "column", children: [_jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsx(Text, { color: "cyan", bold: true, children: "Kritrima AI CLI Help System" }), _jsx(Box, { marginLeft: 2, children: _jsx(Text, { color: "gray", children: "Tab: Switch sections \u2022 1-5: Quick select \u2022 Esc: Close" }) })] }), _jsx(Box, { paddingX: 2, paddingY: 1, children: HELP_SECTIONS.map((section, index) => (_jsx(Box, { marginRight: 2, children: _jsxs(Text, { color: selectedSection === section.id ? 'black' : 'gray', backgroundColor: selectedSection === section.id ? 'cyan' : undefined, bold: selectedSection === section.id, children: [index + 1, ". ", section.title] }) }, section.id))) }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingY: 1, children: renderContent() }), _jsx(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: _jsxs(Text, { color: "gray", children: ["Kritrima AI CLI v", CLI_VERSION, " \u2022 For more help, visit our documentation"] }) })] }));
}
//# sourceMappingURL=help-overlay.js.map