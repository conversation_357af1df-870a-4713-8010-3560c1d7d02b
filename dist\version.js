/**
 * Version Management System
 *
 * Provides dynamic version loading from package.json
 * Ensures version consistency across the application
 */
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
// Read version from package.json at runtime
let CLI_VERSION;
try {
    const packageJsonPath = join(__dirname, '..', 'package.json');
    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    CLI_VERSION = packageJson.version;
}
catch (error) {
    console.warn('Warning: Could not read version from package.json, using fallback');
    CLI_VERSION = '1.0.0';
}
export { CLI_VERSION };
/**
 * Get the current CLI version
 */
export function getVersion() {
    return CLI_VERSION;
}
/**
 * Get version information for display
 */
export function getVersionInfo() {
    return {
        version: CLI_VERSION,
        node: process.version,
        platform: process.platform,
        arch: process.arch
    };
}
//# sourceMappingURL=version.js.map