/**
 * Advanced File Operations & Patch System
 *
 * Handles unified diff processing, file creation, editing, and deletion
 * Supports V4A diff format with conflict resolution and backup system
 */
export interface PatchOperation {
    type: 'create' | 'edit' | 'delete' | 'move';
    path: string;
    content?: string;
    newPath?: string;
    backup?: boolean;
}
export interface PatchResult {
    success: boolean;
    operations: PatchOperation[];
    errors: string[];
    backups: string[];
}
export interface DiffHunk {
    oldStart: number;
    oldCount: number;
    newStart: number;
    newCount: number;
    lines: string[];
}
export interface ParsedDiff {
    oldFile: string;
    newFile: string;
    hunks: DiffHunk[];
    isNew: boolean;
    isDeleted: boolean;
}
/**
 * Process patch string and apply changes
 */
export declare function processPatch(patch: string, workingDirectory?: string, createBackups?: boolean): PatchResult;
/**
 * Parse unified diff format
 */
export declare function parsePatch(patch: string): ParsedDiff[];
/**
 * Restore file from backup
 */
export declare function restoreFromBackup(backupPath: string): boolean;
/**
 * Clean up backup files
 */
export declare function cleanupBackups(directory: string, maxAge?: number): number;
//# sourceMappingURL=apply-patch.d.ts.map