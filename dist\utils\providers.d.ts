/**
 * Provider Configuration System
 *
 * Defines supported AI providers with their configurations
 * Supports built-in providers and custom provider definitions
 */
import type { ProviderConfig, ProviderName } from '../types/index.js';
/**
 * Built-in AI providers with their default configurations
 */
export declare const providers: Record<ProviderName, ProviderConfig>;
/**
 * Get provider configuration by name
 */
export declare function getProviderConfig(providerName: string, customProviders?: Record<string, ProviderConfig>): ProviderConfig | null;
/**
 * Get all available provider names
 */
export declare function getAvailableProviders(customProviders?: Record<string, ProviderConfig>): string[];
/**
 * Validate provider configuration
 */
export declare function validateProviderConfig(config: ProviderConfig): boolean;
/**
 * Get provider display name
 */
export declare function getProviderDisplayName(providerName: string, customProviders?: Record<string, ProviderConfig>): string;
/**
 * Check if provider supports a specific feature
 */
export declare function providerSupportsFeature(providerName: string, feature: 'images' | 'tools' | 'streaming'): boolean;
/**
 * Get default model for provider
 */
export declare function getDefaultModel(providerName: string): string;
//# sourceMappingURL=providers.d.ts.map