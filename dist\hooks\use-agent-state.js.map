{"version": 3, "file": "use-agent-state.js", "sourceRoot": "", "sources": ["../../src/hooks/use-agent-state.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,OAAO,CAAC;AAmCjE,MAAM,UAAU,aAAa,CAAC,UAAgC,EAAE;IAC9D,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAa,MAAM,CAAC,CAAC;IACvD,MAAM,CAAC,gBAAgB,EAAE,mBAAmB,CAAC,GAAG,QAAQ,CAAwB,IAAI,CAAC,CAAC;IACtF,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAoB,IAAI,CAAC,CAAC;IAC5D,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAE5C,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC;IACnC,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;IAE7B,gBAAgB;IAChB,MAAM,MAAM,GAAG,KAAK,KAAK,MAAM,CAAC;IAChC,MAAM,UAAU,GAAG,KAAK,KAAK,UAAU,CAAC;IACxC,MAAM,WAAW,GAAG,KAAK,KAAK,WAAW,CAAC;IAC1C,MAAM,WAAW,GAAG,KAAK,KAAK,WAAW,CAAC;IAC1C,MAAM,QAAQ,GAAG,KAAK,KAAK,OAAO,CAAC;IAEnC,uBAAuB;IACvB,SAAS,CAAC,GAAG,EAAE;QACb,UAAU,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,CAAC;IAC5C,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,gBAAgB;IAChB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,KAAK,EAAE,CAAC;YACV,UAAU,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC;QACtC,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,6BAA6B;IAC7B,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,SAA0B,EAAE,EAAE;QAC/D,QAAQ,CAAC,UAAU,CAAC,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,WAAW,CAAC,CAAC,CAAC,CAAC;QAEf,IAAI,SAAS,EAAE,CAAC;YACd,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,SAAyB,EAAE,EAAE;QAC/D,QAAQ,CAAC,WAAW,CAAC,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,WAAW,CAAC,CAAC,CAAC,CAAC;QACf,mBAAmB,CAAC,SAAS,CAAC,CAAC;IACjC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,cAAc,GAAG,WAAW,CAAC,CAAC,SAA0B,EAAE,EAAE;QAChE,QAAQ,CAAC,WAAW,CAAC,CAAC;QACtB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,WAAW,CAAC,CAAC,CAAC,CAAC;QAEf,IAAI,SAAS,EAAE,CAAC;YACd,mBAAmB,CAAC,SAAS,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,gBAAgB,GAAG,WAAW,CAAC,CAAC,WAAmB,EAAE,EAAE;QAC3D,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IACvD,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,QAAoB,EAAE,EAAE;QACzD,QAAQ,CAAC,OAAO,CAAC,CAAC;QAClB,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACnB,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,QAAQ,GAAG,WAAW,CAAC,CAAC,MAAY,EAAE,EAAE;QAC5C,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,WAAW,CAAC,GAAG,CAAC,CAAC;QAEjB,iDAAiD;QACjD,UAAU,CAAC,GAAG,EAAE;YACd,mBAAmB,CAAC,IAAI,CAAC,CAAC;YAC1B,WAAW,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,UAAU,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC;IAC1C,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE;QAC7B,QAAQ,CAAC,MAAM,CAAC,CAAC;QACjB,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC1B,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,WAAW,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,uBAAuB;IACvB,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,OAAgC,EAAE,EAAE;QACvE,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,GAAG,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACrE,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,OAAO,GAAG,WAAW,CAAC,CAAC,IAAY,EAAE,EAAE;QAC3C,mBAAmB,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI;gBAAE,OAAO,IAAI,CAAC;YAEvB,OAAO;gBACL,GAAG,IAAI;gBACP,KAAK,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC,EAAE;wBAC7B,EAAE,EAAE,QAAQ,IAAI,CAAC,GAAG,EAAE,EAAE;wBACxB,WAAW,EAAE,IAAI;wBACjB,MAAM,EAAE,SAAS;wBACjB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;qBACtB,CAAC;aACH,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,WAAW,CAAC,CAAC,SAAiB,EAAE,EAAE;QACrD,mBAAmB,CAAC,IAAI,CAAC,EAAE;YACzB,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK;gBAAE,OAAO,IAAI,CAAC;YAEtC,MAAM,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC;YACrC,IAAI,YAAY,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5B,YAAY,CAAC,SAAS,CAAC,GAAG;oBACxB,GAAG,YAAY,CAAC,SAAS,CAAC;oBAC1B,MAAM,EAAE,WAAW;oBACnB,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;iBACxB,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,GAAG,IAAI;gBACP,KAAK,EAAE,YAAY;aACpB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,KAAK;QACL,MAAM;QACN,UAAU;QACV,WAAW;QACX,WAAW;QACX,QAAQ;QACR,gBAAgB;QAChB,KAAK;QACL,QAAQ;QAER,aAAa;QACb,cAAc;QACd,cAAc;QACd,WAAW,EAAE,gBAAgB;QAC7B,QAAQ,EAAE,aAAa;QACvB,QAAQ;QACR,KAAK;QAEL,eAAe;QACf,OAAO;QACP,YAAY;KACb,CAAC;AACJ,CAAC;AAUD,MAAM,UAAU,aAAa,CAAC,UAAgC,EAAE;IAC9D,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAmB,EAAE,CAAC,CAAC;IACzD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAmB,EAAE,CAAC,CAAC;IAC3D,MAAM,CAAC,SAAS,EAAE,YAAY,CAAC,GAAG,QAAQ,CAAmB,EAAE,CAAC,CAAC;IAEjE,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,CAAC,CAAC;IAEjD,mBAAmB;IACnB,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,SAAyB,EAAE,EAAE;QAC3D,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IACzC,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,WAAmB,EAAE,EAAE;QAC1D,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC,CAAC;IAC7D,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,SAAS,GAAG,WAAW,CAAC,GAAG,EAAE;QACjC,IAAI,MAAM,CAAC,MAAM,IAAI,aAAa,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACzD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC/B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;QAE5C,OAAO,aAAa,CAAC;IACvB,CAAC,EAAE,CAAC,KAAK,EAAE,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,CAAC,CAAC;IAE1C,MAAM,iBAAiB,GAAG,WAAW,CAAC,CAAC,WAAmB,EAAE,MAAY,EAAE,EAAE;QAC1E,SAAS,CAAC,IAAI,CAAC,EAAE;YACf,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;YACzD,IAAI,SAAS,EAAE,CAAC;gBACd,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,EAAE;wBAC/C,GAAG,SAAS;wBACZ,MAAM,EAAE,WAAW;wBACnB,MAAM;wBACN,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;qBACxB,CAAC,CAAC,CAAC;YACN,CAAC;YACD,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,aAAa,GAAG,WAAW,CAAC,CAAC,WAAmB,EAAE,KAAiB,EAAE,EAAE;QAC3E,SAAS,CAAC,IAAI,CAAC,EAAE;YACf,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;YACzD,IAAI,SAAS,EAAE,CAAC;gBACd,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC,CAAC,GAAG,aAAa,EAAE;wBAC/C,GAAG,SAAS;wBACZ,MAAM,EAAE,QAAQ;wBAChB,KAAK;wBACL,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE;qBACxB,CAAC,CAAC,CAAC;YACN,CAAC;YACD,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,WAAW,CAAC,CAAC;QAClD,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;QACtC,YAAY,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,EAAE;QAChC,QAAQ,CAAC,EAAE,CAAC,CAAC;QACb,SAAS,CAAC,EAAE,CAAC,CAAC;QACd,YAAY,CAAC,EAAE,CAAC,CAAC;IACnB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,0CAA0C;IAC1C,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,MAAM,CAAC,MAAM,GAAG,aAAa,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACtD,MAAM,KAAK,GAAG,UAAU,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;YACvC,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QACnC,CAAC;IACH,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC,CAAC,CAAC;IAE5D,uBAAuB;IACvB,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,aAAa,EAAE,CAAC,KAAK,CAAC,CAAC;IACjC,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;IAErB,OAAO;QACL,KAAK;QACL,MAAM;QACN,SAAS;QAET,UAAU;QACV,eAAe;QACf,SAAS;QACT,iBAAiB;QACjB,aAAa;QACb,cAAc;QACd,QAAQ;QAER,sBAAsB;QACtB,YAAY,EAAE,KAAK,CAAC,MAAM;QAC1B,WAAW,EAAE,MAAM,CAAC,MAAM;QAC1B,cAAc,EAAE,SAAS,CAAC,MAAM;QAChC,YAAY,EAAE,MAAM,CAAC,MAAM,GAAG,aAAa,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;KAChE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe;IAC7B,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAC;QACrC,eAAe,EAAE,CAAC;QAClB,oBAAoB,EAAE,CAAC;QACvB,gBAAgB,EAAE,CAAC;QACnB,oBAAoB,EAAE,CAAC;QACvB,kBAAkB,EAAE,CAAC;QACrB,mBAAmB,EAAE,CAAC;QACtB,iBAAiB,EAAE,CAAC;KACrB,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,WAAW,CAAC,CAClC,OAAgB,EAChB,aAAqB,EACrB,EAAE;QACF,UAAU,CAAC,IAAI,CAAC,EAAE;YAChB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC;YAC1C,MAAM,aAAa,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,oBAAoB,CAAC;YAC1F,MAAM,SAAS,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC;YAC9E,MAAM,YAAY,GAAG,IAAI,CAAC,kBAAkB,GAAG,aAAa,CAAC;YAC7D,MAAM,cAAc,GAAG,YAAY,GAAG,QAAQ,CAAC;YAE/C,0DAA0D;YAC1D,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACvB,MAAM,cAAc,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,iBAAiB,IAAI,GAAG,CAAC,CAAC;YAC7D,MAAM,YAAY,GAAG,cAAc,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,GAAG,cAAc,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAElF,OAAO;gBACL,eAAe,EAAE,QAAQ;gBACzB,oBAAoB,EAAE,aAAa;gBACnC,gBAAgB,EAAE,SAAS;gBAC3B,oBAAoB,EAAE,cAAc;gBACpC,kBAAkB,EAAE,YAAY;gBAChC,mBAAmB,EAAE,YAAY;gBACjC,iBAAiB,EAAE,GAAG;aACvB,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,UAAU,CAAC;YACT,eAAe,EAAE,CAAC;YAClB,oBAAoB,EAAE,CAAC;YACvB,gBAAgB,EAAE,CAAC;YACnB,oBAAoB,EAAE,CAAC;YACvB,kBAAkB,EAAE,CAAC;YACrB,mBAAmB,EAAE,CAAC;YACtB,iBAAiB,EAAE,CAAC;SACrB,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,OAAO;QACL,OAAO;QACP,eAAe;QACf,YAAY;QAEZ,sBAAsB;QACtB,WAAW,EAAE,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YACxC,CAAC,OAAO,CAAC,oBAAoB,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACpE,WAAW,EAAE,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;YACxC,CAAC,OAAO,CAAC,gBAAgB,GAAG,OAAO,CAAC,eAAe,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;KACjE,CAAC;AACJ,CAAC"}