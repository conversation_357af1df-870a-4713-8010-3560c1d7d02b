{"version": 3, "file": "help-overlay.js", "sourceRoot": "", "sources": ["../../../src/components/overlays/help-overlay.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AACxC,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAC/D,OAAO,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAC;AAe/C,MAAM,aAAa,GAAsB;IACvC;QACE,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,8BAA8B;KAC5C;IACD;QACE,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,gBAAgB;QACvB,WAAW,EAAE,mCAAmC;KACjD;IACD;QACE,EAAE,EAAE,WAAW;QACf,KAAK,EAAE,oBAAoB;QAC3B,WAAW,EAAE,uCAAuC;KACrD;IACD;QACE,EAAE,EAAE,UAAU;QACd,KAAK,EAAE,UAAU;QACjB,WAAW,EAAE,oCAAoC;KAClD;IACD;QACE,EAAE,EAAE,MAAM;QACV,KAAK,EAAE,eAAe;QACtB,WAAW,EAAE,sCAAsC;KACpD;CACF,CAAC;AAEF,MAAM,UAAU,WAAW,CAAC,EAAE,OAAO,EAAE,OAAO,EAAoB;IAChE,MAAM,CAAC,eAAe,EAAE,kBAAkB,CAAC,GAAG,QAAQ,CAAc,UAAU,CAAC,CAAC;IAEhF,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,CAAC,OAAO;YAAE,OAAO;QAErB,gBAAgB;QAChB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;YACV,OAAO;QACT,CAAC;QAED,qBAAqB;QACrB,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,MAAM,YAAY,GAAG,aAAa,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,CAAC;YAC5E,MAAM,SAAS,GAAG,CAAC,YAAY,GAAG,CAAC,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC;YAC5D,kBAAkB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,0BAA0B;QAC1B,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC;YACjC,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAClC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC;gBAC/C,kBAAkB,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;YAC9C,CAAC;YACD,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,CAC3B,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,6CACR,WAAW,IACnC,EAEP,KAAC,IAAI,IAAC,YAAY,EAAE,CAAC,mJAGd,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,iCAE9C,EAEP,KAAC,IAAI,6EAA6D,EAClE,KAAC,IAAI,wEAAwD,EAC7D,KAAC,IAAI,sEAAsD,EAC3D,KAAC,IAAI,uDAAuC,EAC5C,KAAC,IAAI,oEAAoD,EAEzD,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,8BAE9C,EAEP,KAAC,IAAI,kFAAkE,EACvE,KAAC,IAAI,uEAAuD,EAC5D,KAAC,IAAI,sEAAsD,EAC3D,KAAC,IAAI,qEAAqD,EAC1D,KAAC,IAAI,kEAAkD,EACvD,KAAC,IAAI,uDAAuC,IACxC,CACP,CAAC;IAEF;;OAEG;IACH,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,CAC3B,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,0CAEhC,EAEN,cAAc,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,CAClC,MAAC,GAAG,IAAmB,YAAY,EAAE,CAAC,aACpC,KAAC,GAAG,IAAC,KAAK,EAAE,EAAE,YACZ,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,kBACpB,GAAG,CAAC,OAAO,GACP,GACH,EACN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,YACd,KAAC,IAAI,cAAE,GAAG,CAAC,WAAW,GAAQ,GAC1B,KARE,GAAG,CAAC,OAAO,CASf,CACP,CAAC,EAEF,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC,+BAE9C,EAEP,KAAC,IAAI,qEAAqD,EAC1D,KAAC,IAAI,kFAAkE,EACvE,KAAC,IAAI,6DAA6C,EAClD,KAAC,IAAI,gEAAsD,IACvD,CACP,CAAC;IAEF;;OAEG;IACH,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC,CAC5B,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,oCAEhC,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,4BAAoB,EAC1D,KAAC,IAAI,gFAAsD,EAC3D,KAAC,IAAI,yEAAyD,EAC9D,KAAC,IAAI,uEAAuD,EAC5D,KAAC,IAAI,iEAAiD,EACtD,KAAC,IAAI,uEAAuD,EAE5D,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,iCAAyB,EAC/D,KAAC,IAAI,mEAAmD,EACxD,KAAC,IAAI,sEAAsD,EAC3D,KAAC,IAAI,4DAA4C,EACjD,KAAC,IAAI,+DAA+C,EACpD,KAAC,IAAI,uDAAuC,EAC5C,KAAC,IAAI,iDAAiC,EACtC,KAAC,IAAI,iDAAiC,EAEtC,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,6BAAqB,EAC3D,KAAC,IAAI,6DAA6C,EAClD,KAAC,IAAI,+DAA+C,EACpD,KAAC,IAAI,yDAAyC,EAE9C,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,0BAAkB,EACxD,KAAC,IAAI,iEAAiD,EACtD,KAAC,IAAI,iEAAiD,EACtD,KAAC,IAAI,iEAAiD,EACtD,KAAC,IAAI,mEAAmD,EACxD,KAAC,IAAI,2DAA2C,IAC5C,CACP,CAAC;IAEF;;OAEG;IACH,MAAM,cAAc,GAAG,GAAG,EAAE,CAAC,CAC3B,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,mCAEhC,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,kCAA0B,EAChE,KAAC,IAAI,mEAAmD,EACxD,KAAC,IAAI,8DAA8C,EACnD,KAAC,IAAI,wDAAwC,EAC7C,KAAC,IAAI,0DAA0C,EAE/C,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,8BAAsB,EAC5D,KAAC,IAAI,8DAA8C,EACnD,KAAC,IAAI,kEAAkD,EACvD,KAAC,IAAI,+CAA+B,EACpC,KAAC,IAAI,yDAAyC,EAC9C,KAAC,IAAI,iDAAiC,EAEtC,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,qCAA6B,EACnE,KAAC,IAAI,uEAAuD,EAC5D,KAAC,IAAI,+EAA+D,EACpE,KAAC,IAAI,0EAA0D,EAC/D,KAAC,IAAI,yDAAyC,EAE9C,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,oCAA4B,EAClE,KAAC,IAAI,uDAAuC,EAC5C,KAAC,IAAI,0DAA0C,EAC/C,KAAC,IAAI,oDAAoC,EACzC,KAAC,IAAI,sDAAsC,EAE3C,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,8BAAsB,EAC5D,KAAC,IAAI,4DAA4C,EACjD,KAAC,IAAI,6DAA6C,EAClD,KAAC,IAAI,0DAA0C,EAC/C,KAAC,IAAI,mDAAmC,IACpC,CACP,CAAC;IAEF;;OAEG;IACH,MAAM,UAAU,GAAG,GAAG,EAAE,CAAC,CACvB,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aACzB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,QAAC,YAAY,EAAE,CAAC,uCAEhC,EAEP,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,mCAA2B,EACjE,KAAC,IAAI,yEAAyD,EAC9D,KAAC,IAAI,sEAAsD,EAC3D,KAAC,IAAI,gEAAgD,EACrD,KAAC,IAAI,kFAAkE,EACvE,KAAC,IAAI,sEAAsD,EAE3D,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,yCAAiC,EACvE,KAAC,IAAI,uEAAuD,EAC5D,KAAC,IAAI,yDAAyC,EAC9C,KAAC,IAAI,mEAAmD,EACxD,KAAC,IAAI,yDAAyC,EAC9C,KAAC,IAAI,gEAAgD,EAErD,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,iCAAyB,EAC/D,KAAC,IAAI,0EAA0D,EAC/D,KAAC,IAAI,0DAA0C,EAC/C,KAAC,IAAI,6DAA6C,EAClD,KAAC,IAAI,wEAAwD,EAC7D,KAAC,IAAI,0EAA0D,EAE/D,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,QAAC,SAAS,EAAE,CAAC,kCAA0B,EAChE,KAAC,IAAI,kEAAkD,EACvD,KAAC,IAAI,kEAAkD,EACvD,KAAC,IAAI,qEAAqD,EAC1D,KAAC,IAAI,+DAA+C,EACpD,KAAC,IAAI,gEAAgD,IACjD,CACP,CAAC;IAEF;;OAEG;IACH,MAAM,aAAa,GAAG,GAAG,EAAE;QACzB,QAAQ,eAAe,EAAE,CAAC;YACxB,KAAK,UAAU,CAAC,CAAC,OAAO,cAAc,EAAE,CAAC;YACzC,KAAK,UAAU,CAAC,CAAC,OAAO,cAAc,EAAE,CAAC;YACzC,KAAK,WAAW,CAAC,CAAC,OAAO,eAAe,EAAE,CAAC;YAC3C,KAAK,UAAU,CAAC,CAAC,OAAO,cAAc,EAAE,CAAC;YACzC,KAAK,MAAM,CAAC,CAAC,OAAO,UAAU,EAAE,CAAC;YACjC,OAAO,CAAC,CAAC,OAAO,cAAc,EAAE,CAAC;QACnC,CAAC;IACH,CAAC,CAAC;IAEF,IAAI,CAAC,OAAO,EAAE,CAAC;QACb,OAAO,IAAI,CAAC;IACd,CAAC;IAED,OAAO,CACL,MAAC,GAAG,IACF,QAAQ,EAAC,UAAU,EACnB,GAAG,EAAE,CAAC,EACN,IAAI,EAAE,CAAC,EACP,KAAK,EAAE,CAAC,EACR,MAAM,EAAE,CAAC,EACT,WAAW,EAAC,QAAQ,EACpB,WAAW,EAAC,MAAM,EAClB,eAAe,EAAC,OAAO,EACvB,aAAa,EAAC,QAAQ,aAGtB,MAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,aACpE,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,kDAEhB,EACP,KAAC,GAAG,IAAC,UAAU,EAAE,CAAC,YAChB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,gFAEX,GACH,IACF,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YAC1B,aAAa,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CACrC,KAAC,GAAG,IAAkB,WAAW,EAAE,CAAC,YAClC,MAAC,IAAI,IACH,KAAK,EAAE,eAAe,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,EACxD,eAAe,EAAE,eAAe,KAAK,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EACpE,IAAI,EAAE,eAAe,KAAK,OAAO,CAAC,EAAE,aAEnC,KAAK,GAAG,CAAC,QAAI,OAAO,CAAC,KAAK,IACtB,IAPC,OAAO,CAAC,EAAE,CAQd,CACP,CAAC,GACE,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,YACvC,aAAa,EAAE,GACZ,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,WAAW,EAAC,QAAQ,EAAC,WAAW,EAAC,MAAM,YACpE,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,kCACE,WAAW,sDACxB,GACH,IACF,CACP,CAAC;AACJ,CAAC"}