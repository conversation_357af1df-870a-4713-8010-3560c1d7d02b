/**
 * Shell Command Execution Handler
 *
 * Processes shell commands with platform adaptation, security validation,
 * and comprehensive output capture
 */
import type { ExecInput, ExecResult, AppConfig, ApprovalPolicy } from '../../types/index.js';
/**
 * Execute shell command with comprehensive handling
 */
export declare function handleExecCommand(input: ExecInput, config: AppConfig, approvalPolicy: ApprovalPolicy, additionalWritableRoots?: string[], signal?: AbortSignal): Promise<ExecResult>;
/**
 * Validate command safety
 */
export declare function validateCommandSafety(command: string[], workdir: string, config: AppConfig): {
    safe: boolean;
    warnings: string[];
    blockers: string[];
};
/**
 * Format command output for display
 */
export declare function formatCommandOutput(result: ExecResult): string;
/**
 * Estimate command execution time
 */
export declare function estimateExecutionTime(command: string[]): number;
/**
 * Check if command modifies files
 */
export declare function commandModifiesFiles(command: string[]): boolean;
/**
 * Get command description for user
 */
export declare function getCommandDescription(command: string[]): string;
//# sourceMappingURL=handle-exec-command.d.ts.map