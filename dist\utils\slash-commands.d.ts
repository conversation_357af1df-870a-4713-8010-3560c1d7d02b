/**
 * Slash Command System
 *
 * Provides built-in commands for the CLI interface
 * Supports auto-completion and parameter handling
 */
export interface SlashCommand {
    command: string;
    description: string;
    parameters?: string[];
    examples?: string[];
    handler?: (args: string[]) => Promise<string> | string;
}
/**
 * Available slash commands
 */
export declare const SLASH_COMMANDS: SlashCommand[];
/**
 * Find slash command by name
 */
export declare function findSlashCommand(commandName: string): SlashCommand | null;
/**
 * Get all slash command names for auto-completion
 */
export declare function getSlashCommandNames(): string[];
/**
 * Check if text starts with a slash command
 */
export declare function isSlashCommand(text: string): boolean;
/**
 * Parse slash command from text
 */
export declare function parseSlashCommand(text: string): {
    command: string;
    args: string[];
    isValid: boolean;
};
/**
 * Execute slash command
 */
export declare function executeSlashCommand(text: string): Promise<{
    success: boolean;
    result: string;
    command?: string;
}>;
/**
 * Get auto-completion suggestions for slash commands
 */
export declare function getSlashCommandSuggestions(input: string): string[];
//# sourceMappingURL=slash-commands.d.ts.map