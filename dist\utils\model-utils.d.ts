/**
 * Model Management System
 *
 * Handles dynamic model discovery, validation, and management
 * Provides caching for performance and context length calculation
 */
import type { ModelInfo } from '../types/index.js';
/**
 * Fetch available models from provider
 */
export declare function fetchModels(provider: string): Promise<string[]>;
/**
 * Validate if model exists for provider
 */
export declare function validateModel(model: string, provider: string): Promise<boolean>;
/**
 * Get model information including context length and capabilities
 */
export declare function getModelInfo(model: string, provider: string): ModelInfo;
/**
 * Calculate token usage for context management
 */
export declare function estimateTokens(text: string): number;
/**
 * Check if context fits within model limits
 */
export declare function checkContextLimit(content: string, model: string, provider: string, buffer?: number): {
    fits: boolean;
    tokens: number;
    limit: number;
};
/**
 * Get recommended models for provider
 */
export declare function getRecommendedModels(provider: string): string[];
/**
 * Clear model cache
 */
export declare function clearModelCache(): void;
/**
 * Get cached models without API call
 */
export declare function getCachedModels(provider: string): string[] | null;
//# sourceMappingURL=model-utils.d.ts.map