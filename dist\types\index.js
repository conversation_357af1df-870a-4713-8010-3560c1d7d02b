/**
 * Core Types and Interfaces
 *
 * Defines the fundamental types used throughout the Kritrima AI CLI system
 */
// ============================================================================
// Error Types
// ============================================================================
export class KritrimaError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'KritrimaError';
    }
}
export class NetworkError extends KritrimaError {
    constructor(message, details) {
        super(message, 'NETWORK_ERROR', details);
    }
}
export class ConfigError extends KritrimaError {
    constructor(message, details) {
        super(message, 'CONFIG_ERROR', details);
    }
}
export class SecurityError extends KritrimaError {
    constructor(message, details) {
        super(message, 'SECURITY_ERROR', details);
    }
}
//# sourceMappingURL=index.js.map