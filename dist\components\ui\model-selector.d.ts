/**
 * Model Selection Component
 *
 * Interactive model and provider selection interface
 * Supports real-time model discovery and validation
 */
interface ModelSelectorProps {
    currentProvider: string;
    currentModel: string;
    onSelect: (provider: string, model: string) => void;
    onCancel: () => void;
}
export declare function ModelSelector({ currentProvider, currentModel, onSelect, onCancel }: ModelSelectorProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=model-selector.d.ts.map