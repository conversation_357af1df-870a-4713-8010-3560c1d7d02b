/**
 * Raw Command Execution
 *
 * Fallback execution without sandboxing for platforms that don't support
 * advanced sandboxing features. Still applies basic security checks.
 */
import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
/**
 * Execute command without sandboxing
 */
export declare function exec(input: ExecInput, config: AppConfig): Promise<ExecResult>;
/**
 * Check if command is safe for execution
 */
export declare function isCommandSafe(command: string[], config: AppConfig): boolean;
/**
 * Get command execution environment info
 */
export declare function getExecutionEnvironment(): {
    platform: string;
    shell: string;
    sandboxing: boolean;
    restrictions: string[];
};
/**
 * Test command execution capabilities
 */
export declare function testExecution(): Promise<{
    success: boolean;
    capabilities: string[];
    limitations: string[];
}>;
//# sourceMappingURL=raw-exec.d.ts.map