import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
/**
 * Command History Overlay
 *
 * Browse and search through command history with keyboard navigation
 * Supports filtering, selection, and execution of previous commands
 */
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput } from 'ink';
import { getHistory, searchHistory, getHistoryStats } from '../../utils/storage/command-history.js';
export function HistoryOverlay({ onSelectCommand, onClose, visible }) {
    const [selectedIndex, setSelectedIndex] = useState(0);
    const [searchQuery, setSearchQuery] = useState('');
    const [searchMode, setSearchMode] = useState(false);
    const [filteredHistory, setFilteredHistory] = useState([]);
    const [stats, setStats] = useState(null);
    // Load history and stats
    useEffect(() => {
        if (visible) {
            const history = getHistory();
            const historyStats = getHistoryStats();
            setFilteredHistory(history.reverse()); // Show newest first
            setStats(historyStats);
            setSelectedIndex(0);
            setSearchQuery('');
            setSearchMode(false);
        }
    }, [visible]);
    // Update filtered history when search query changes
    useEffect(() => {
        if (searchQuery.trim()) {
            const results = searchHistory(searchQuery);
            setFilteredHistory(results.reverse()); // Show newest first
            setSelectedIndex(0);
        }
        else {
            const history = getHistory();
            setFilteredHistory(history.reverse());
            setSelectedIndex(0);
        }
    }, [searchQuery]);
    // Handle keyboard input
    useInput((input, key) => {
        if (!visible)
            return;
        // Close overlay
        if (key.escape) {
            if (searchMode && searchQuery) {
                // Clear search first
                setSearchQuery('');
                setSearchMode(false);
            }
            else {
                onClose();
            }
            return;
        }
        // Toggle search mode
        if (key.ctrl && input === 'f') {
            setSearchMode(!searchMode);
            return;
        }
        // Handle search mode
        if (searchMode) {
            if (key.backspace || key.delete) {
                setSearchQuery(prev => prev.slice(0, -1));
                return;
            }
            if (key.return) {
                setSearchMode(false);
                return;
            }
            if (input && !key.ctrl && !key.meta) {
                setSearchQuery(prev => prev + input);
                return;
            }
        }
        else {
            // Navigation mode
            if (key.upArrow) {
                setSelectedIndex(Math.max(0, selectedIndex - 1));
                return;
            }
            if (key.downArrow) {
                setSelectedIndex(Math.min(filteredHistory.length - 1, selectedIndex + 1));
                return;
            }
            if (key.pageUp) {
                setSelectedIndex(Math.max(0, selectedIndex - 10));
                return;
            }
            if (key.pageDown) {
                setSelectedIndex(Math.min(filteredHistory.length - 1, selectedIndex + 10));
                return;
            }
            if (key.home) {
                setSelectedIndex(0);
                return;
            }
            if (key.end) {
                setSelectedIndex(filteredHistory.length - 1);
                return;
            }
            // Select command
            if (key.return) {
                const selectedEntry = filteredHistory[selectedIndex];
                if (selectedEntry) {
                    onSelectCommand(selectedEntry.command);
                    onClose();
                }
                return;
            }
            // Start search
            if (input === '/') {
                setSearchMode(true);
                return;
            }
        }
    });
    /**
     * Format timestamp for display
     */
    const formatTimestamp = useCallback((timestamp) => {
        const date = new Date(timestamp);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        if (diffDays === 0) {
            return date.toLocaleTimeString();
        }
        else if (diffDays === 1) {
            return 'Yesterday';
        }
        else if (diffDays < 7) {
            return `${diffDays} days ago`;
        }
        else {
            return date.toLocaleDateString();
        }
    }, []);
    /**
     * Get success indicator
     */
    const getSuccessIndicator = useCallback((success) => {
        if (success === true)
            return '✓';
        if (success === false)
            return '✗';
        return '•';
    }, []);
    if (!visible) {
        return null;
    }
    return (_jsxs(Box, { position: "absolute", top: 1, left: 1, right: 1, bottom: 1, borderStyle: "double", borderColor: "cyan", backgroundColor: "black", flexDirection: "column", children: [_jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsx(Text, { color: "cyan", bold: true, children: "Command History" }), stats && (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: "gray", children: [stats.totalCommands, " total \u2022 ", stats.uniqueCommands, " unique \u2022 ", stats.successfulCommands, " successful"] }) }))] }), _jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: searchMode ? "yellow" : "gray", children: [_jsx(Text, { color: "blue", children: "Search: " }), _jsx(Text, { color: searchMode ? "yellow" : "white", children: searchQuery || (searchMode ? "█" : "Type / to search, Ctrl+F to toggle") }), searchQuery && (_jsxs(Text, { color: "gray", marginLeft: 2, children: ["(", filteredHistory.length, " results)"] }))] }), _jsx(Box, { flexGrow: 1, paddingX: 2, paddingY: 1, flexDirection: "column", children: filteredHistory.length === 0 ? (_jsx(Text, { color: "gray", children: searchQuery ? 'No commands found matching your search.' : 'No command history available.' })) : (_jsx(_Fragment, { children: (() => {
                        const maxVisible = 15; // Approximate visible items
                        const startIndex = Math.max(0, selectedIndex - Math.floor(maxVisible / 2));
                        const endIndex = Math.min(filteredHistory.length, startIndex + maxVisible);
                        const visibleItems = filteredHistory.slice(startIndex, endIndex);
                        return visibleItems.map((entry, index) => {
                            const actualIndex = startIndex + index;
                            const isSelected = actualIndex === selectedIndex;
                            return (_jsxs(Box, { marginBottom: 1, children: [_jsx(Box, { width: 3, children: _jsx(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: getSuccessIndicator(entry.success) }) }), _jsx(Box, { width: 12, children: _jsx(Text, { color: isSelected ? "black" : "gray", backgroundColor: isSelected ? "cyan" : undefined, children: formatTimestamp(entry.timestamp) }) }), _jsx(Box, { flexGrow: 1, children: _jsx(Text, { color: isSelected ? "black" : "white", backgroundColor: isSelected ? "cyan" : undefined, bold: isSelected, children: entry.command.length > 60 ? `${entry.command.substring(0, 57)}...` : entry.command }) })] }, `${entry.timestamp}-${entry.command}`));
                        });
                    })() })) }), _jsxs(Box, { paddingX: 2, paddingY: 1, borderStyle: "single", borderColor: "gray", children: [_jsx(Text, { color: "gray", children: "\u2191\u2193: Navigate \u2022 Enter: Select \u2022 /: Search \u2022 Ctrl+F: Toggle search \u2022 Esc: Close" }), filteredHistory.length > 0 && (_jsx(Box, { marginLeft: 2, children: _jsxs(Text, { color: "gray", children: [selectedIndex + 1, "/", filteredHistory.length] }) }))] })] }));
}
//# sourceMappingURL=history-overlay.js.map