/**
 * Auto-completion System
 *
 * Provides intelligent auto-completion for commands, file paths, and slash commands
 * Supports fuzzy matching and context-aware suggestions
 */
export interface CompletionResult {
    suggestions: string[];
    prefix: string;
    hasMore: boolean;
    type: 'command' | 'file' | 'slash' | 'history' | 'mixed';
}
export interface CompletionOptions {
    maxSuggestions?: number;
    includeHidden?: boolean;
    fuzzyMatch?: boolean;
    contextAware?: boolean;
}
/**
 * Get auto-completion suggestions for input
 */
export declare function getCompletions(input: string, cursorPosition?: number, options?: CompletionOptions): CompletionResult;
/**
 * Get file type completions
 */
export declare function getFileTypeCompletions(directory?: string, extensions?: string[]): string[];
/**
 * Get directory completions
 */
export declare function getDirectoryCompletions(basePath?: string, includeHidden?: boolean): string[];
/**
 * Auto-completion manager class
 */
export declare class AutoCompleteManager {
    private cache;
    private cacheTimeout;
    /**
     * Get completions with caching
     */
    getCompletions(input: string, cursorPosition?: number, options?: CompletionOptions): CompletionResult;
    /**
     * Clear completion cache
     */
    clearCache(): void;
    /**
     * Get cache size
     */
    getCacheSize(): number;
}
export declare const autoCompleteManager: AutoCompleteManager;
/**
 * Get file system suggestions for a query
 */
export declare function getFileSystemSuggestions(query: string, basePath?: string, maxResults?: number): string[];
//# sourceMappingURL=autocomplete.d.ts.map