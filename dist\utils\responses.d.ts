/**
 * Response Processing System
 *
 * Handles real-time streaming responses from AI providers
 * Processes different event types and manages conversation flow
 */
import type { OpenAI } from 'openai';
import type { ResponseItem, ResponseOutputItem, ResponseFunctionToolCall } from '../types/index.js';
export interface ResponseEvent {
    type: 'response.created' | 'response.output_text.delta' | 'response.function_call_arguments.done' | 'response.completed' | 'response.error';
    delta?: string;
    content?: string;
    functionCall?: ResponseFunctionToolCall;
    error?: string;
    metadata?: any;
}
export interface ResponseCreateInput {
    messages: Array<{
        role: 'user' | 'assistant' | 'system';
        content: string | Array<{
            type: string;
            text?: string;
            image_url?: {
                url: string;
            };
        }>;
    }>;
    model: string;
    temperature?: number;
    max_tokens?: number;
    tools?: Array<{
        type: 'function';
        function: {
            name: string;
            description: string;
            parameters: any;
        };
    }>;
    tool_choice?: 'auto' | 'none' | {
        type: 'function';
        function: {
            name: string;
        };
    };
    stream?: boolean;
}
/**
 * Stream responses from OpenAI completion
 */
export declare function streamResponses(input: ResponseCreateInput, completion: AsyncIterable<OpenAI.ChatCompletionChunk>): AsyncGenerator<ResponseEvent>;
/**
 * Create response output item from completed response
 */
export declare function createResponseOutputItem(content: string, metadata: {
    model: string;
    provider: string;
    tokens?: number;
    thinkingTime?: number;
}): ResponseOutputItem;
/**
 * Process streaming completion without tools
 */
export declare function processStreamingCompletion(client: OpenAI, input: ResponseCreateInput, onDelta?: (delta: string) => void, onComplete?: (content: string) => void, onError?: (error: string) => void): Promise<string>;
/**
 * Process non-streaming completion
 */
export declare function processCompletion(client: OpenAI, input: ResponseCreateInput): Promise<{
    content: string;
    functionCall?: ResponseFunctionToolCall;
    usage?: {
        prompt_tokens: number;
        completion_tokens: number;
        total_tokens: number;
    };
}>;
/**
 * Convert messages to OpenAI format
 */
export declare function convertMessagesToOpenAI(items: ResponseItem[]): any[];
/**
 * Estimate token count for messages
 */
export declare function estimateTokenCount(messages: Array<{
    role: string;
    content: string | Array<any>;
}>): number;
//# sourceMappingURL=responses.d.ts.map