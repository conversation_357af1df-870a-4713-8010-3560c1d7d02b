/**
 * Full-Context Mode Implementation
 *
 * Provides comprehensive context gathering and analysis for complex tasks
 * Automatically includes relevant files, documentation, and system information
 */
import { readFileSync, existsSync, readdirSync, statSync } from 'fs';
import { join, relative } from 'path';
import { logInfo, logError } from '../logger/log.js';
import { getConfig } from '../config.js';
const DEFAULT_OPTIONS = {
    includeFiles: true,
    includeGitInfo: true,
    includeSystemInfo: true,
    includeProjectStructure: true,
    maxFileSize: 100 * 1024, // 100KB per file
    maxTotalSize: 10 * 1024 * 1024, // 10MB total
    fileExtensions: [
        '.js', '.ts', '.jsx', '.tsx', '.py', '.java', '.cpp', '.c', '.h',
        '.css', '.html', '.xml', '.json', '.yaml', '.yml', '.toml', '.ini',
        '.md', '.txt', '.sh', '.bat', '.ps1', '.sql', '.go', '.rs', '.php',
        '.rb', '.swift', '.kt', '.scala', '.clj', '.hs', '.elm', '.dart',
        '.vue', '.svelte', '.dockerfile', '.gitignore', '.env'
    ],
    excludePatterns: [
        'node_modules', '.git', 'dist', 'build', 'target', '.next',
        '.nuxt', '.vscode', '.idea', '*.log', '*.tmp', '*.cache',
        'coverage', '.nyc_output', '__pycache__', '*.pyc', '.pytest_cache'
    ],
    maxDepth: 5
};
/**
 * Gather full context for a project or directory
 */
export async function gatherFullContext(workingDir = process.cwd(), userQuery = '', options = {}) {
    const finalOptions = { ...DEFAULT_OPTIONS, ...options };
    logInfo('Gathering full context', {
        workingDir,
        userQuery: userQuery.substring(0, 100),
        options: finalOptions
    });
    const result = {
        content: [],
        analysis: {
            projectType: 'unknown',
            technologies: [],
            entryPoints: [],
            configFiles: [],
            documentation: [],
            testFiles: [],
            buildFiles: []
        },
        includedFiles: [],
        totalSize: 0,
        warnings: []
    };
    try {
        // Add user query as initial context
        if (userQuery.trim()) {
            result.content.push({
                type: 'input_text',
                text: `User Query: ${userQuery}\n\n`
            });
        }
        // Analyze project structure
        const analysis = await analyzeProject(workingDir, finalOptions);
        result.analysis = analysis;
        // Add project analysis
        result.content.push({
            type: 'input_text',
            text: formatProjectAnalysis(analysis)
        });
        // Include system information
        if (finalOptions.includeSystemInfo) {
            const systemInfo = gatherSystemInfo();
            result.content.push({
                type: 'input_text',
                text: systemInfo
            });
        }
        // Include Git information
        if (finalOptions.includeGitInfo) {
            const gitInfo = await gatherGitInfo(workingDir);
            if (gitInfo) {
                result.content.push({
                    type: 'input_text',
                    text: gitInfo
                });
            }
        }
        // Include project structure
        if (finalOptions.includeProjectStructure) {
            const structure = generateProjectStructure(workingDir, finalOptions);
            result.content.push({
                type: 'input_text',
                text: structure
            });
        }
        // Include relevant files
        if (finalOptions.includeFiles) {
            const files = await gatherRelevantFiles(workingDir, userQuery, analysis, finalOptions);
            for (const file of files) {
                if (result.totalSize + file.size > finalOptions.maxTotalSize) {
                    result.warnings.push(`Reached maximum total size limit (${finalOptions.maxTotalSize} bytes)`);
                    break;
                }
                result.content.push({
                    type: 'input_text',
                    text: file.content
                });
                result.includedFiles.push(file.path);
                result.totalSize += file.size;
            }
        }
        logInfo('Full context gathered', {
            contentItems: result.content.length,
            includedFiles: result.includedFiles.length,
            totalSize: result.totalSize,
            warnings: result.warnings.length
        });
        return result;
    }
    catch (error) {
        logError('Failed to gather full context', error instanceof Error ? error : new Error(String(error)));
        // Return minimal context on error
        return {
            content: [{
                    type: 'input_text',
                    text: `Error gathering full context: ${error instanceof Error ? error.message : 'Unknown error'}\n\nUser Query: ${userQuery}`
                }],
            analysis: result.analysis,
            includedFiles: [],
            totalSize: 0,
            warnings: [`Failed to gather full context: ${error instanceof Error ? error.message : 'Unknown error'}`]
        };
    }
}
/**
 * Analyze project to determine type and technologies
 */
async function analyzeProject(workingDir, options) {
    const analysis = {
        projectType: 'unknown',
        technologies: [],
        entryPoints: [],
        configFiles: [],
        documentation: [],
        testFiles: [],
        buildFiles: []
    };
    try {
        const files = readdirSync(workingDir);
        // Detect project type and technologies
        for (const file of files) {
            const filePath = join(workingDir, file);
            const stats = statSync(filePath);
            if (stats.isFile()) {
                // Configuration files
                if (['package.json', 'yarn.lock', 'package-lock.json'].includes(file)) {
                    analysis.projectType = 'node';
                    analysis.technologies.push('Node.js');
                    analysis.configFiles.push(file);
                }
                if (['requirements.txt', 'setup.py', 'pyproject.toml', 'Pipfile'].includes(file)) {
                    analysis.projectType = 'python';
                    analysis.technologies.push('Python');
                    analysis.configFiles.push(file);
                }
                if (['Cargo.toml', 'Cargo.lock'].includes(file)) {
                    analysis.projectType = 'rust';
                    analysis.technologies.push('Rust');
                    analysis.configFiles.push(file);
                }
                if (['go.mod', 'go.sum'].includes(file)) {
                    analysis.projectType = 'go';
                    analysis.technologies.push('Go');
                    analysis.configFiles.push(file);
                }
                // Documentation
                if (['README.md', 'README.txt', 'CHANGELOG.md', 'CONTRIBUTING.md'].includes(file)) {
                    analysis.documentation.push(file);
                }
                // Build files
                if (['Makefile', 'Dockerfile', 'docker-compose.yml', '.github'].includes(file)) {
                    analysis.buildFiles.push(file);
                }
            }
        }
        // Find entry points
        const entryPoints = findEntryPoints(workingDir, analysis.projectType);
        analysis.entryPoints = entryPoints;
        // Find test files
        const testFiles = findTestFiles(workingDir, options);
        analysis.testFiles = testFiles;
        return analysis;
    }
    catch (error) {
        logError('Failed to analyze project', error instanceof Error ? error : new Error(String(error)));
        return analysis;
    }
}
/**
 * Find project entry points
 */
function findEntryPoints(workingDir, projectType) {
    const entryPoints = [];
    try {
        const commonEntryPoints = [
            'index.js', 'index.ts', 'main.js', 'main.ts', 'app.js', 'app.ts',
            'server.js', 'server.ts', 'main.py', '__main__.py', 'app.py',
            'main.go', 'main.rs', 'src/main.rs', 'lib.rs', 'src/lib.rs'
        ];
        for (const entry of commonEntryPoints) {
            const entryPath = join(workingDir, entry);
            if (existsSync(entryPath)) {
                entryPoints.push(entry);
            }
        }
        // Check package.json for main entry
        if (projectType === 'node') {
            const packageJsonPath = join(workingDir, 'package.json');
            if (existsSync(packageJsonPath)) {
                try {
                    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
                    if (packageJson.main && !entryPoints.includes(packageJson.main)) {
                        entryPoints.push(packageJson.main);
                    }
                }
                catch (error) {
                    // Ignore JSON parse errors
                }
            }
        }
    }
    catch (error) {
        logError('Failed to find entry points', error instanceof Error ? error : new Error(String(error)));
    }
    return entryPoints;
}
/**
 * Find test files
 */
function findTestFiles(workingDir, options) {
    const testFiles = [];
    try {
        const testPatterns = [
            /\.test\./,
            /\.spec\./,
            /_test\./,
            /test_.*\.py$/,
            /.*_test\.go$/
        ];
        const searchDir = (dir, depth = 0) => {
            if (depth > options.maxDepth)
                return;
            const files = readdirSync(dir);
            for (const file of files) {
                const filePath = join(dir, file);
                const stats = statSync(filePath);
                if (stats.isDirectory()) {
                    if (!options.excludePatterns.some(pattern => file.includes(pattern))) {
                        searchDir(filePath, depth + 1);
                    }
                }
                else if (stats.isFile()) {
                    if (testPatterns.some(pattern => pattern.test(file))) {
                        testFiles.push(relative(workingDir, filePath));
                    }
                }
            }
        };
        searchDir(workingDir);
    }
    catch (error) {
        logError('Failed to find test files', error instanceof Error ? error : new Error(String(error)));
    }
    return testFiles;
}
/**
 * Gather system information
 */
function gatherSystemInfo() {
    const config = getConfig();
    return [
        '=== SYSTEM INFORMATION ===',
        `Platform: ${process.platform} (${process.arch})`,
        `Node.js: ${process.version}`,
        `Working Directory: ${process.cwd()}`,
        `Current Provider: ${config.provider}`,
        `Current Model: ${config.model}`,
        `Approval Mode: ${config.approvalMode}`,
        `Timestamp: ${new Date().toISOString()}`,
        ''
    ].join('\n');
}
/**
 * Gather Git information
 */
async function gatherGitInfo(workingDir) {
    try {
        const { execSync } = require('child_process');
        // Check if it's a git repository
        const gitDir = join(workingDir, '.git');
        if (!existsSync(gitDir)) {
            return null;
        }
        const branch = execSync('git branch --show-current', {
            cwd: workingDir,
            encoding: 'utf8'
        }).trim();
        const status = execSync('git status --porcelain', {
            cwd: workingDir,
            encoding: 'utf8'
        }).trim();
        const lastCommit = execSync('git log -1 --oneline', {
            cwd: workingDir,
            encoding: 'utf8'
        }).trim();
        return [
            '=== GIT INFORMATION ===',
            `Branch: ${branch}`,
            `Last Commit: ${lastCommit}`,
            status ? `Status:\n${status}` : 'Status: Clean working directory',
            ''
        ].join('\n');
    }
    catch (error) {
        return null; // Not a git repository or git not available
    }
}
/**
 * Generate project structure
 */
function generateProjectStructure(workingDir, options) {
    const structure = ['=== PROJECT STRUCTURE ==='];
    const buildTree = (dir, prefix = '', depth = 0) => {
        if (depth > options.maxDepth)
            return;
        try {
            const files = readdirSync(dir).sort();
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const filePath = join(dir, file);
                const isLast = i === files.length - 1;
                const stats = statSync(filePath);
                // Skip excluded patterns
                if (options.excludePatterns.some(pattern => file.includes(pattern))) {
                    continue;
                }
                const connector = isLast ? '└── ' : '├── ';
                structure.push(`${prefix}${connector}${file}`);
                if (stats.isDirectory()) {
                    const newPrefix = prefix + (isLast ? '    ' : '│   ');
                    buildTree(filePath, newPrefix, depth + 1);
                }
            }
        }
        catch (error) {
            // Skip directories we can't read
        }
    };
    buildTree(workingDir);
    structure.push('');
    return structure.join('\n');
}
/**
 * Format project analysis
 */
function formatProjectAnalysis(analysis) {
    return [
        '=== PROJECT ANALYSIS ===',
        `Project Type: ${analysis.projectType}`,
        `Technologies: ${analysis.technologies.join(', ') || 'None detected'}`,
        `Entry Points: ${analysis.entryPoints.join(', ') || 'None found'}`,
        `Config Files: ${analysis.configFiles.join(', ') || 'None found'}`,
        `Documentation: ${analysis.documentation.join(', ') || 'None found'}`,
        `Test Files: ${analysis.testFiles.length} found`,
        `Build Files: ${analysis.buildFiles.join(', ') || 'None found'}`,
        ''
    ].join('\n');
}
/**
 * Gather relevant files based on query and analysis
 */
async function gatherRelevantFiles(workingDir, userQuery, analysis, options) {
    const files = [];
    // Priority files to include
    const priorityFiles = [
        ...analysis.entryPoints,
        ...analysis.configFiles,
        ...analysis.documentation.slice(0, 3), // Limit documentation
        ...analysis.testFiles.slice(0, 5) // Limit test files
    ];
    // Add priority files first
    for (const file of priorityFiles) {
        const filePath = join(workingDir, file);
        if (existsSync(filePath)) {
            try {
                const content = await formatFileContent(filePath, file);
                const size = Buffer.byteLength(content, 'utf8');
                if (size <= options.maxFileSize) {
                    files.push({ path: file, content, size });
                }
            }
            catch (error) {
                // Skip files we can't read
            }
        }
    }
    return files;
}
/**
 * Format file content with metadata
 */
async function formatFileContent(filePath, relativePath) {
    const content = readFileSync(filePath, 'utf-8');
    const stats = statSync(filePath);
    return [
        `=== FILE: ${relativePath} ===`,
        `Size: ${stats.size} bytes`,
        `Modified: ${stats.mtime.toISOString()}`,
        '---',
        content,
        '',
        ''
    ].join('\n');
}
//# sourceMappingURL=full-context-mode.js.map