/**
 * Package Manager Detection System
 *
 * Auto-detects package managers based on installation path and environment
 * Supports npm, pnpm, bun, yarn, and deno
 */
import { execSync } from 'child_process';
import { existsSync } from 'fs';
import { dirname, basename } from 'path';
/**
 * Detect package manager by analyzing the installation path
 */
export async function detectInstallerByPath() {
    try {
        // Get the path to the current executable
        const execPath = process.argv[0];
        const execDir = dirname(execPath);
        const execName = basename(execPath);
        // Check for specific package manager indicators in path
        if (execDir.includes('pnpm') || execName.includes('pnpm')) {
            return 'pnpm';
        }
        if (execDir.includes('bun') || execName.includes('bun')) {
            return 'bun';
        }
        if (execDir.includes('yarn') || execName.includes('yarn')) {
            return 'yarn';
        }
        if (execDir.includes('deno') || execName.includes('deno')) {
            return 'deno';
        }
        // Check environment variables
        if (process.env.npm_config_user_agent) {
            const userAgent = process.env.npm_config_user_agent.toLowerCase();
            if (userAgent.includes('pnpm'))
                return 'pnpm';
            if (userAgent.includes('yarn'))
                return 'yarn';
            if (userAgent.includes('bun'))
                return 'bun';
            if (userAgent.includes('npm'))
                return 'npm';
        }
        // Check for package manager executables in PATH
        const detectedPM = await detectByExecutable();
        if (detectedPM) {
            return detectedPM;
        }
        // Default to npm
        return 'npm';
    }
    catch (error) {
        return 'npm'; // Fallback to npm
    }
}
/**
 * Detect package manager by checking for executables
 */
async function detectByExecutable() {
    const packageManagers = ['pnpm', 'bun', 'yarn', 'npm'];
    for (const pm of packageManagers) {
        if (await isExecutableAvailable(pm)) {
            return pm;
        }
    }
    return undefined;
}
/**
 * Check if executable is available in PATH
 */
async function isExecutableAvailable(executable) {
    try {
        const command = process.platform === 'win32' ? 'where' : 'which';
        execSync(`${command} ${executable}`, { stdio: 'ignore', timeout: 5000 });
        return true;
    }
    catch (error) {
        return false;
    }
}
/**
 * Get package manager version
 */
export async function getPackageManagerVersion(pm) {
    try {
        const result = execSync(`${pm} --version`, {
            encoding: 'utf-8',
            timeout: 5000,
            stdio: 'pipe'
        });
        return result.trim();
    }
    catch (error) {
        return null;
    }
}
/**
 * Get installation command for package manager
 */
export function getInstallCommand(pm, packageName, global = false) {
    const globalFlag = global ? '-g' : '';
    switch (pm) {
        case 'npm':
            return `npm install ${globalFlag} ${packageName}`;
        case 'pnpm':
            return `pnpm ${global ? 'add -g' : 'add'} ${packageName}`;
        case 'yarn':
            return global ? `yarn global add ${packageName}` : `yarn add ${packageName}`;
        case 'bun':
            return `bun ${global ? 'add -g' : 'add'} ${packageName}`;
        case 'deno':
            return `deno install ${packageName}`;
        default:
            return `npm install ${globalFlag} ${packageName}`;
    }
}
/**
 * Get update command for package manager
 */
export function getUpdateCommand(pm, packageName, global = false) {
    switch (pm) {
        case 'npm':
            return `npm update ${global ? '-g' : ''} ${packageName}`;
        case 'pnpm':
            return `pnpm ${global ? 'update -g' : 'update'} ${packageName}`;
        case 'yarn':
            return global ? `yarn global upgrade ${packageName}` : `yarn upgrade ${packageName}`;
        case 'bun':
            return `bun update ${global ? '-g' : ''} ${packageName}`;
        case 'deno':
            return `deno install --force ${packageName}`;
        default:
            return `npm update ${global ? '-g' : ''} ${packageName}`;
    }
}
/**
 * Get uninstall command for package manager
 */
export function getUninstallCommand(pm, packageName, global = false) {
    switch (pm) {
        case 'npm':
            return `npm uninstall ${global ? '-g' : ''} ${packageName}`;
        case 'pnpm':
            return `pnpm ${global ? 'remove -g' : 'remove'} ${packageName}`;
        case 'yarn':
            return global ? `yarn global remove ${packageName}` : `yarn remove ${packageName}`;
        case 'bun':
            return `bun remove ${global ? '-g' : ''} ${packageName}`;
        case 'deno':
            return `deno uninstall ${packageName}`;
        default:
            return `npm uninstall ${global ? '-g' : ''} ${packageName}`;
    }
}
/**
 * Detect package manager from lock files
 */
export function detectFromLockFiles(projectPath = process.cwd()) {
    const lockFiles = [
        { file: 'pnpm-lock.yaml', pm: 'pnpm' },
        { file: 'bun.lockb', pm: 'bun' },
        { file: 'yarn.lock', pm: 'yarn' },
        { file: 'package-lock.json', pm: 'npm' },
        { file: 'deno.lock', pm: 'deno' }
    ];
    for (const { file, pm } of lockFiles) {
        if (existsSync(`${projectPath}/${file}`)) {
            return pm;
        }
    }
    return undefined;
}
/**
 * Get package manager information
 */
export async function getPackageManagerInfo(pm) {
    const detectedPM = pm || await detectInstallerByPath() || 'npm';
    const version = await getPackageManagerVersion(detectedPM);
    const available = await isExecutableAvailable(detectedPM);
    return {
        name: detectedPM,
        version,
        available,
        installCommand: getInstallCommand(detectedPM, 'kritrima-ai-cli', true),
        updateCommand: getUpdateCommand(detectedPM, 'kritrima-ai-cli', true)
    };
}
/**
 * Get all available package managers
 */
export async function getAllPackageManagers() {
    const packageManagers = ['npm', 'pnpm', 'yarn', 'bun', 'deno'];
    const results = [];
    for (const pm of packageManagers) {
        const version = await getPackageManagerVersion(pm);
        const available = await isExecutableAvailable(pm);
        results.push({
            name: pm,
            version,
            available
        });
    }
    return results;
}
/**
 * Recommend best package manager
 */
export async function recommendPackageManager() {
    const allPMs = await getAllPackageManagers();
    const availablePMs = allPMs.filter(pm => pm.available);
    if (availablePMs.length === 0) {
        return {
            recommended: 'npm',
            reason: 'No package managers detected, npm is the default',
            alternatives: []
        };
    }
    // Preference order: pnpm > bun > yarn > npm
    const preferenceOrder = ['pnpm', 'bun', 'yarn', 'npm'];
    for (const preferred of preferenceOrder) {
        const found = availablePMs.find(pm => pm.name === preferred);
        if (found) {
            const alternatives = availablePMs
                .filter(pm => pm.name !== preferred)
                .map(pm => pm.name);
            const reasons = {
                pnpm: 'Fast, disk space efficient, and strict dependency management',
                bun: 'Extremely fast JavaScript runtime and package manager',
                yarn: 'Fast, reliable, and secure dependency management',
                npm: 'Default Node.js package manager, widely supported',
                deno: 'Modern runtime with built-in package management'
            };
            return {
                recommended: preferred,
                reason: reasons[preferred],
                alternatives
            };
        }
    }
    // Fallback to first available
    const fallback = availablePMs[0];
    return {
        recommended: fallback.name,
        reason: 'First available package manager',
        alternatives: availablePMs.slice(1).map(pm => pm.name)
    };
}
//# sourceMappingURL=package-manager-detector.js.map