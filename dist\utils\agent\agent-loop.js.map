{"version": 3, "file": "agent-loop.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/agent-loop.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAGH,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AACzD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AAY/D,OAAO,EAAE,iBAAiB,EAAE,MAAM,0BAA0B,CAAC;AAC7D,OAAO,EAAE,uBAAuB,EAAE,kBAAkB,EAAE,MAAM,iBAAiB,CAAC;AAyB9E;;GAEG;AACH,MAAM,OAAO,SAAS;IACZ,KAAK,CAAS;IACd,QAAQ,CAAS;IACjB,GAAG,CAAS;IACZ,cAAc,CAAiB;IAC/B,UAAU,GAAwB,EAAE,CAAC;IACrC,oBAAoB,GAAG,CAAC,CAAC;IACzB,uBAAuB,CAAW;IAClC,MAAM,CAAY;IAE1B,YAAY,MAAuB;QACjC,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC;QAChC,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;QAC5C,IAAI,CAAC,uBAAuB,GAAG,MAAM,CAAC,uBAAuB,IAAI,EAAE,CAAC;QACpE,IAAI,CAAC,MAAM,GAAG,UAAU,EAAE,CAAC;QAE3B,uBAAuB;QACvB,IAAI,CAAC,GAAG,GAAG,kBAAkB,CAAC;YAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ;YACvB,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;IACL,CAAC;IAuBD,KAAK,CAAC,WAAW,CACf,SAA4B,EAC5B,qBAOI,EAAE,EACN,gBAAwB,EAAE;QAE1B,+BAA+B;QAC/B,IAAI,SAAS,GAAuB,EAAE,CAAC;QACvC,IAAI,mBAAmB,GAAG,aAAa,CAAC;QACxC,IAAI,UAAU,GAAG,KAAK,CAAC;QAEvB,IAAI,WAAW,IAAI,kBAAkB,IAAI,eAAe,IAAI,kBAAkB,EAAE,CAAC;YAC/E,qBAAqB;YACrB,MAAM,OAAO,GAAG,kBAAyB,CAAC;YAC1C,SAAS,GAAG,OAAO,CAAC,SAAS,IAAI,EAAE,CAAC;YACpC,mBAAmB,GAAG,OAAO,CAAC,aAAa,IAAI,EAAE,CAAC;YAClD,UAAU,GAAG,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;YACzC,gFAAgF;QAClF,CAAC;aAAM,CAAC;YACN,0BAA0B;YAC1B,SAAS,GAAG,kBAAwC,CAAC;QACvD,CAAC;QAED,0CAA0C;QAC1C,IAAI,UAAU,EAAE,CAAC;YACf,mBAAmB,GAAG,CAAC,CAAC;QAC1B,CAAC;QACD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,OAAO,GAAmB,EAAE,CAAC;QAEnC,IAAI,CAAC;YACH,+BAA+B;YAC/B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAChC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAExB,iBAAiB,CAAC,YAAY,EAAE;gBAC9B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC,CAAC,CAAC;YAEH,IAAI,SAAS,GAAG,CAAC,CAAC;YAClB,OAAO,SAAS,GAAG,mBAAmB,EAAE,CAAC;gBACvC,SAAS,EAAE,CAAC;gBAEZ,iBAAiB,CAAC,iBAAiB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;gBAEpD,sCAAsC;gBACtC,MAAM,QAAQ,GAAG,uBAAuB,CAAC,CAAC,GAAG,IAAI,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC,CAAC;gBAE1E,qBAAqB;gBACrB,MAAM,UAAU,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;gBAChD,iBAAiB,CAAC,aAAa,EAAE,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC,CAAC;gBAEzD,gBAAgB;gBAChB,MAAM,KAAK,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBAEvC,kBAAkB;gBAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBAEtC,IAAI,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;wBACxD,KAAK,EAAE,IAAI,CAAC,KAAK;wBACjB,QAAQ;wBACR,KAAK,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;wBAC3C,WAAW,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;wBAClD,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,GAAG;wBAC3C,UAAU,EAAE,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,IAAI;wBACzC,MAAM,EAAE,KAAK;qBACP,CAAC,CAAC;oBAEV,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,kBAAkB,CAAC;oBACtD,IAAI,CAAC,oBAAoB,IAAI,aAAa,CAAC;oBAE3C,MAAM,MAAM,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC,CAAC;oBACvC,IAAI,CAAC,MAAM,EAAE,CAAC;wBACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;oBACjD,CAAC;oBAED,iBAAiB;oBACjB,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;oBAC9C,IAAI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;wBACnB,SAAS,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,CAAC;wBAC7B,SAAS,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;wBAEhC,MAAM,iBAAiB,GAAuB;4BAC5C,IAAI,EAAE,WAAW;4BACjB,OAAO;4BACP,IAAI,EAAE,QAAQ;4BACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;4BACrB,QAAQ,EAAE;gCACR,KAAK,EAAE,IAAI,CAAC,KAAK;gCACjB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gCACvB,YAAY,EAAE,aAAa;6BAC5B;yBACF,CAAC;wBAEF,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;oBAClC,CAAC;oBAED,wBAAwB;oBACxB,IAAI,MAAM,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;wBAC/B,KAAK,MAAM,QAAQ,IAAI,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC;4BACjD,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;gCACjC,MAAM,YAAY,GAA6B;oCAC7C,EAAE,EAAE,QAAQ,CAAC,EAAE;oCACf,IAAI,EAAE,QAAQ,CAAC,QAAQ,CAAC,IAAI;oCAC5B,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,SAAS;oCACtC,IAAI,EAAE,eAAe;oCACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;iCACtB,CAAC;gCAEF,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gCAC3B,SAAS,CAAC,UAAU,EAAE,CAAC,YAAY,CAAC,CAAC;gCAErC,wBAAwB;gCACxB,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gCAC3E,OAAO,CAAC,IAAI,CAAC,GAAG,WAAW,CAAC,CAAC;4BAC/B,CAAC;wBACH,CAAC;oBACH,CAAC;yBAAM,CAAC;wBACN,8CAA8C;wBAC9C,iBAAiB,CAAC,eAAe,EAAE;4BACjC,UAAU,EAAE,SAAS;4BACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;yBAClC,CAAC,CAAC;wBACH,OAAO,OAAO,CAAC;oBACjB,CAAC;gBAEH,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;oBAC9E,QAAQ,CAAC,6BAA6B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;oBAClG,SAAS,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;oBAClC,MAAM,KAAK,CAAC;gBACd,CAAC;YACH,CAAC;YAED,iBAAiB,CAAC,qBAAqB,EAAE,EAAE,aAAa,EAAE,mBAAmB,EAAE,CAAC,CAAC;YACjF,OAAO,OAAO,CAAC;QAEjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,QAAQ,CAAC,mBAAmB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;YACxF,SAAS,CAAC,OAAO,EAAE,CAAC,YAAY,CAAC,CAAC;YAClC,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,YAAsC,EACtC,SAA6B;QAE7B,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,YAAY,CAAC;QAErD,IAAI,CAAC;YACH,kBAAkB;YAClB,IAAI,IAAS,CAAC;YACd,IAAI,CAAC;gBACH,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,IAAI,KAAK,CAAC,+BAA+B,UAAU,EAAE,CAAC,CAAC;YAC/D,CAAC;YAED,IAAI,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,aAAa,EAAE,CAAC;gBAC/C,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,IAAI,EAAE,YAAY,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;YACzE,CAAC;YAED,MAAM,IAAI,KAAK,CAAC,qBAAqB,IAAI,EAAE,CAAC,CAAC;QAE/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,WAAW,GAAuB;gBACtC,EAAE,EAAE,YAAY,CAAC,EAAE;gBACnB,MAAM,EAAE,UAAU,YAAY,EAAE;gBAChC,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,OAAO,CAAC,WAAW,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,kBAAkB,CAC9B,IAAS,EACT,UAAkB,EAClB,SAA6B;QAE7B,IAAI,CAAC;YACH,MAAM,SAAS,GAAc;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,EAAE;gBAC3B,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE;gBACtC,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,KAAK;aAC/B,CAAC;YAEF,qCAAqC;YACrC,MAAM,aAAa,GAAG,IAAI,CAAC,cAAc,KAAK,SAAS;gBAClC,CAAC,IAAI,CAAC,cAAc,KAAK,WAAW,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;YAErG,IAAI,aAAa,IAAI,SAAS,CAAC,sBAAsB,EAAE,CAAC;gBACtD,MAAM,QAAQ,GAAG,MAAM,SAAS,CAAC,sBAAsB,CAAC,SAAS,CAAC,OAAO,EAAE,SAAS,CAAC,OAAO,IAAI,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC/G,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACd,MAAM,YAAY,GAAuB;wBACvC,EAAE,EAAE,UAAU;wBACd,MAAM,EAAE,kCAAkC;wBAC1C,OAAO,EAAE,KAAK;wBACd,IAAI,EAAE,aAAa;wBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;wBACrB,QAAQ,EAAE;4BACR,OAAO,EAAE,SAAS,CAAC,OAAO;4BAC1B,OAAO,EAAE,SAAS,CAAC,OAAO;yBAC3B;qBACF,CAAC;oBACF,OAAO,CAAC,YAAY,CAAC,CAAC;gBACxB,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,MAAM,UAAU,GAAG,MAAM,iBAAiB,CACxC,SAAS,EACT,IAAI,CAAC,MAAM,EACX,IAAI,CAAC,cAAc,EACnB,IAAI,CAAC,uBAAuB,CAC7B,CAAC;YAEF,MAAM,UAAU,GAAuB;gBACrC,EAAE,EAAE,UAAU;gBACd,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,QAAQ,EAAE;oBACR,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,OAAO,EAAE,UAAU,CAAC,OAAO;oBAC3B,QAAQ,EAAE,UAAU,CAAC,QAAQ;oBAC7B,QAAQ,EAAE,UAAU,CAAC,QAAQ;iBAC9B;aACF,CAAC;YAEF,SAAS,CAAC,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC;YACrC,OAAO,CAAC,UAAU,CAAC,CAAC;QAEtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;YAC9E,MAAM,WAAW,GAAuB;gBACtC,EAAE,EAAE,UAAU;gBACd,MAAM,EAAE,4BAA4B,YAAY,EAAE;gBAClD,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,aAAa;gBACnB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;aACtB,CAAC;YAEF,OAAO,CAAC,WAAW,CAAC,CAAC;QACvB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,OAAiB;QACrC,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;QACpD,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;QAC9C,OAAO,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;IAC5C,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,MAAM,KAAK,GAAmB,EAAE,CAAC;QAEjC,aAAa;QACb,KAAK,CAAC,IAAI,CAAC;YACT,IAAI,EAAE,UAAU;YAChB,QAAQ,EAAE;gBACR,IAAI,EAAE,OAAO;gBACb,WAAW,EAAE,sIAAsI;gBACnJ,UAAU,EAAE;oBACV,IAAI,EAAE,QAAQ;oBACd,UAAU,EAAE;wBACV,OAAO,EAAE;4BACP,IAAI,EAAE,OAAO;4BACb,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;4BACzB,WAAW,EAAE,uEAAuE;yBACrF;wBACD,OAAO,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,mFAAmF;yBACjG;wBACD,OAAO,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,WAAW,EAAE,uDAAuD;yBACrE;qBACF;oBACD,QAAQ,EAAE,CAAC,SAAS,CAAC;iBACtB;aACF;SACF,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACf,CAAC;IAED;;OAEG;IACH,aAAa;QACX,OAAO,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,eAAe;QACb,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;IACvB,CAAC;IAED;;OAEG;IACH,yBAAyB;QACvB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACnC,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,SAAmC;QAC9C,IAAI,SAAS,CAAC,KAAK;YAAE,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC;QAClD,IAAI,SAAS,CAAC,QAAQ;YAAE,IAAI,CAAC,QAAQ,GAAG,SAAS,CAAC,QAAQ,CAAC;QAC3D,IAAI,SAAS,CAAC,cAAc;YAAE,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,cAAc,CAAC;QAC7E,IAAI,SAAS,CAAC,uBAAuB;YAAE,IAAI,CAAC,uBAAuB,GAAG,SAAS,CAAC,uBAAuB,CAAC;QAExG,6CAA6C;QAC7C,IAAI,SAAS,CAAC,QAAQ,IAAI,SAAS,CAAC,KAAK,EAAE,CAAC;YAC1C,IAAI,CAAC,GAAG,GAAG,kBAAkB,CAAC;gBAC5B,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,OAAO,EAAE,SAAS,CAAC,OAAO;aAC3B,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF"}