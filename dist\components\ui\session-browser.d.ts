/**
 * Session Browser Component
 *
 * Interactive browser for saved conversation sessions
 * Supports session loading, deletion, and export
 */
interface SessionBrowserProps {
    onLoad: (sessionId: string) => void;
    onCancel: () => void;
}
export declare function SessionBrowser({ onLoad, onCancel }: SessionBrowserProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=session-browser.d.ts.map