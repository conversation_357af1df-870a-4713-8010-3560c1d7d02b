{"version": 3, "file": "platform-commands.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/platform-commands.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH;;GAEG;AACH,MAAM,WAAW,GAA2B;IAC1C,kBAAkB;IAClB,IAAI,EAAE,KAAK;IACX,KAAK,EAAE,MAAM;IACb,IAAI,EAAE,KAAK;IACX,IAAI,EAAE,MAAM;IACZ,IAAI,EAAE,MAAM;IACZ,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,IAAI;IACb,OAAO,EAAE,OAAO;IAChB,KAAK,EAAE,IAAI;IACX,OAAO,EAAE,OAAO;IAEhB,kBAAkB;IAClB,MAAM,EAAE,SAAS;IACjB,MAAM,EAAE,MAAM;IACd,MAAM,EAAE,MAAM;IACd,IAAI,EAAE,eAAe;IAErB,qBAAqB;IACrB,IAAI,EAAE,UAAU;IAChB,MAAM,EAAE,UAAU;IAClB,SAAS,EAAE,UAAU;IAErB,UAAU;IACV,MAAM,EAAE,MAAM,EAAE,8BAA8B;IAC9C,MAAM,EAAE,MAAM,EAAE,+BAA+B;IAE/C,UAAU;IACV,KAAK,EAAE,KAAK,EAAE,8BAA8B;IAC5C,OAAO,EAAE,KAAK,EAAE,yBAAyB;IAEzC,cAAc;IACd,OAAO,EAAE,KAAK;IACd,QAAQ,EAAE,QAAQ;IAClB,IAAI,EAAE,QAAQ;IACd,MAAM,EAAE,MAAM;IACd,QAAQ,EAAE,YAAY;CACvB,CAAC;AAEF;;GAEG;AACH,MAAM,YAAY,GAAiD;IACjE,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE;QACd,wCAAwC;QACxC,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,IAAI,CAAC;gBACV,KAAK,KAAK,CAAC;gBACX,KAAK,KAAK;oBACR,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,iBAAiB;oBACrC,MAAM;gBACR,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,oBAAoB;oBACxC,MAAM;gBACR,KAAK,IAAI;oBACP,wCAAwC;oBACxC,MAAM;gBACR,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;oBAChC,MAAM;gBACR,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe;oBACrC,MAAM;gBACR,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe;oBACrC,MAAM;gBACR;oBACE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpB,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;QACf,wCAAwC;QACxC,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD,CAAC;IAED,KAAK,EAAE,CAAC,IAAI,EAAE,EAAE;QACd,gCAAgC;QAChC,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,IAAI,CAAC;gBACV,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,YAAY;oBAChC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ;oBAC5B,MAAM;gBACR,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;oBACpC,MAAM;gBACR,KAAK,IAAI;oBACP,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;oBAC7B,MAAM;gBACR;oBACE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACpB,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,SAAS,EAAE,CAAC,IAAI,EAAE,EAAE;QAClB,sCAAsC;QACtC,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,KAAK,GAAa,EAAE,CAAC;QACzB,IAAI,OAAO,GAAG,KAAK,CAAC;QAEpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAEpB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,IAAI;oBACP,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,mBAAmB;oBAC3C,MAAM;gBACR,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACP,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,sBAAsB;oBAC9C,MAAM;gBACR,KAAK,IAAI;oBACP,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;oBACvC,MAAM;gBACR,KAAK,IAAI;oBACP,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,eAAe;oBACvC,MAAM;gBACR,KAAK,IAAI;oBACP,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,qBAAqB;oBAC7C,MAAM;gBACR,KAAK,IAAI;oBACP,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,gBAAgB;oBACxC,MAAM;gBACR;oBACE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,IAAI,CAAC,OAAO,EAAE,CAAC;4BACb,OAAO,GAAG,GAAG,CAAC;wBAChB,CAAC;6BAAM,CAAC;4BACN,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBAClB,CAAC;oBACH,CAAC;YACL,CAAC;QACH,CAAC;QAED,IAAI,OAAO,EAAE,CAAC;YACZ,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QAED,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAC7B,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE;QACnB,qCAAqC;QACrC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACP,wCAAwC;oBACxC,MAAM;gBACR,KAAK,IAAI;oBACP,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,cAAc;oBAChD,MAAM;gBACR,KAAK,IAAI;oBACP,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,cAAc;oBAC/C,MAAM;gBACR;oBACE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACzB,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE;QACnB,uCAAuC;QACvC,MAAM,YAAY,GAAa,EAAE,CAAC;QAElC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;YAEpB,QAAQ,GAAG,EAAE,CAAC;gBACZ,KAAK,IAAI;oBACP,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ;oBACjC,MAAM;gBACR,KAAK,OAAO,CAAC;gBACb,KAAK,KAAK;oBACR,mBAAmB;oBACnB,MAAM;gBACR;oBACE,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;wBACzB,oBAAoB;wBACpB,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;oBACjC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,YAAY,CAAC;IACtB,CAAC;CACF,CAAC;AAEF;;GAEG;AACH,MAAM,UAAU,uBAAuB,CAAC,OAAiB;IACvD,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,6CAA6C;QAC7C,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;IACtB,CAAC;IAED,sBAAsB;IACtB,MAAM,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,OAAO,CAAC;IAC/B,MAAM,QAAQ,GAAG,GAAG,CAAC,WAAW,EAAE,CAAC;IAEnC,iCAAiC;IACjC,MAAM,SAAS,GAAG,WAAW,CAAC,QAAQ,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,EAAE,CAAC;QACf,qCAAqC;QACrC,OAAO,CAAC,GAAG,OAAO,CAAC,CAAC;IACtB,CAAC;IAED,sCAAsC;IACtC,MAAM,SAAS,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC;IAC1C,MAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAEtD,OAAO,CAAC,SAAS,EAAE,GAAG,UAAU,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,kBAAkB,CAAC,OAAe;IAChD,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAEvC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,4BAA4B;QAC5B,MAAM,eAAe,GAAG;YACtB,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO;YAC/D,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK;YAClE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM;SAC3C,CAAC;QAEF,2CAA2C;QAC3C,IAAI,eAAe,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,sCAAsC;QACtC,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1B,OAAO,IAAI,CAAC;QACd,CAAC;QAED,gCAAgC;QAChC,MAAM,qBAAqB,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QAC5D,IAAI,qBAAqB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC7C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;SAAM,CAAC;QACN,wDAAwD;QACxD,MAAM,kBAAkB,GAAG;YACzB,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;YACxE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM;YACrE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,QAAQ;SAC1D,CAAC;QAEF,IAAI,kBAAkB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC1C,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED,qDAAqD;IACrD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAAe;IACnD,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IACvC,MAAM,WAAW,GAAa,EAAE,CAAC;IAEjC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,+BAA+B;QAC/B,MAAM,kBAAkB,GAA6B;YACnD,IAAI,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC;YAC9B,KAAK,EAAE,CAAC,MAAM,EAAE,aAAa,CAAC;YAC9B,IAAI,EAAE,CAAC,KAAK,EAAE,aAAa,CAAC;YAC5B,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;YAC3B,IAAI,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC;YAC3B,MAAM,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;YACpC,IAAI,EAAE,CAAC,UAAU,EAAE,aAAa,CAAC;YACjC,MAAM,EAAE,CAAC,UAAU,EAAE,cAAc,CAAC;YACpC,OAAO,EAAE,CAAC,OAAO,EAAE,aAAa,CAAC;YACjC,MAAM,EAAE,CAAC,MAAM,EAAE,mBAAmB,CAAC;YACrC,MAAM,EAAE,CAAC,MAAM,EAAE,mBAAmB,CAAC;SACtC,CAAC;QAEF,IAAI,kBAAkB,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjC,WAAW,CAAC,IAAI,CAAC,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;SAAM,CAAC;QACN,4BAA4B;QAC5B,MAAM,eAAe,GAA6B;YAChD,KAAK,EAAE,CAAC,IAAI,CAAC;YACb,MAAM,EAAE,CAAC,KAAK,CAAC;YACf,KAAK,EAAE,CAAC,IAAI,CAAC;YACb,MAAM,EAAE,CAAC,IAAI,CAAC;YACd,MAAM,EAAE,CAAC,IAAI,CAAC;YACd,SAAS,EAAE,CAAC,MAAM,CAAC;YACnB,UAAU,EAAE,CAAC,IAAI,CAAC;YAClB,UAAU,EAAE,CAAC,MAAM,CAAC;YACpB,OAAO,EAAE,CAAC,OAAO,CAAC;SACnB,CAAC;QAEF,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC9B,WAAW,CAAC,IAAI,CAAC,GAAG,eAAe,CAAC,QAAQ,CAAC,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,OAAO,WAAW,CAAC;AACrB,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,OAAe;IAC5C,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;IAEvC,MAAM,SAAS,GAA2B;QACxC,IAAI,EAAE,yEAAyE;QAC/E,KAAK,EAAE,qEAAqE;QAC5E,KAAK,EAAE,8CAA8C;QACrD,MAAM,EAAE,+CAA+C;QACvD,IAAI,EAAE,+DAA+D;QACrE,KAAK,EAAE,mDAAmD;QAC1D,MAAM,EAAE,qDAAqD;QAC7D,SAAS,EAAE,wDAAwD;QACnE,IAAI,EAAE,mDAAmD;QACzD,UAAU,EAAE,yDAAyD;QACrE,MAAM,EAAE,wCAAwC;QAChD,UAAU,EAAE,iDAAiD;KAC9D,CAAC;IAEF,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,kCAAkC,OAAO,EAAE,CAAC;AAC5E,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,aAAa,CAAC,IAAY;IACxC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,eAAe;IAC7B,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvB,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACtB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,cAAc,CAAC,GAAW;IACxC,IAAI,OAAO,CAAC,QAAQ,KAAK,OAAO,EAAE,CAAC;QACjC,mBAAmB;QACnB,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC3C,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC;QACxC,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;SAAM,CAAC;QACN,gBAAgB;QAChB,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAChE,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,CAAC;QAC7C,CAAC;QACD,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC"}