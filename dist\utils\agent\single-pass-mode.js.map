{"version": 3, "file": "single-pass-mode.js", "sourceRoot": "", "sources": ["../../../src/utils/agent/single-pass-mode.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,EAAE,iBAAiB,EAAE,MAAM,wBAAwB,CAAC;AAoC3D,MAAM,eAAe,GAAsB;IACzC,cAAc,EAAE,IAAI;IACpB,SAAS,EAAE,IAAI;IACf,WAAW,EAAE,GAAG,EAAE,mDAAmD;IACrE,mBAAmB,EAAE,IAAI;IACzB,aAAa,EAAE,IAAI;IACnB,cAAc,EAAE,IAAI;IACpB,eAAe,EAAE,KAAK,CAAC,4BAA4B;CACpD,CAAC;AAEF;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,SAAiB,EACjB,SAAoB,EACpB,aAAqB,OAAO,CAAC,GAAG,EAAE,EAClC,UAAsC,EAAE;IAExC,MAAM,YAAY,GAAG,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,CAAC;IACxD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,OAAO,CAAC,gCAAgC,EAAE;QACxC,SAAS,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;QACtC,UAAU;QACV,OAAO,EAAE,YAAY;KACtB,CAAC,CAAC;IAEH,MAAM,MAAM,GAAqB;QAC/B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE,EAAE;QACT,aAAa,EAAE,CAAC;QAChB,UAAU,EAAE,CAAC;QACb,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,MAAM,EAAE,EAAE;KACX,CAAC;IAEF,IAAI,CAAC;QACH,iCAAiC;QACjC,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,cAAc,GAAG,MAAM,oBAAoB,CAC/C,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,CACb,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,QAAQ,GAAG,cAAc,CAAC;YACxC,MAAM,CAAC,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC;YAE/C,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC5B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,0BAA0B,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;gBACrE,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,qBAAqB;QACrB,IAAI,YAAY,CAAC,cAAc,EAAE,CAAC;YAChC,MAAM,eAAe,GAAG,MAAM,qBAAqB,CACjD,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACZ,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,CAC/B,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,SAAS,GAAG,eAAe,CAAC;YAC1C,MAAM,CAAC,UAAU,IAAI,eAAe,CAAC,UAAU,CAAC;YAChD,MAAM,CAAC,KAAK,GAAG,MAAM,uBAAuB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;YAErE,IAAI,CAAC,eAAe,CAAC,OAAO,EAAE,CAAC;gBAC7B,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;gBACvE,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,mCAAmC;QACnC,IAAI,YAAY,CAAC,eAAe,EAAE,CAAC;YACjC,MAAM,gBAAgB,GAAG,MAAM,sBAAsB,CACnD,SAAS,EACT,SAAS,EACT,UAAU,EACV,YAAY,EACZ,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,MAAM,CAChC,CAAC;YAEF,MAAM,CAAC,MAAM,CAAC,UAAU,GAAG,gBAAgB,CAAC;YAC5C,MAAM,CAAC,UAAU,IAAI,gBAAgB,CAAC,UAAU,CAAC;YAEjD,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,CAAC;gBAC9B,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,4BAA4B,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC;YAC7E,CAAC;QACH,CAAC;QAED,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;QACtB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE9C,OAAO,CAAC,iCAAiC,EAAE;YACzC,OAAO,EAAE,MAAM,CAAC,OAAO;YACvB,aAAa,EAAE,MAAM,CAAC,aAAa;YACnC,UAAU,EAAE,MAAM,CAAC,UAAU;YAC7B,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YAClC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,MAAM;SAC3B,CAAC,CAAC;QAEH,OAAO,MAAM,CAAC;IAEhB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAC9E,QAAQ,CAAC,8BAA8B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAEnG,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAC9C,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,iCAAiC,YAAY,EAAE,CAAC,CAAC;QAEpE,OAAO,MAAM,CAAC;IAChB,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,oBAAoB,CACjC,SAAiB,EACjB,SAAoB,EACpB,UAAkB,EAClB,OAA0B;IAE1B,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,CAAC;QACH,OAAO,CAAC,0BAA0B,CAAC,CAAC;QAEpC,8BAA8B;QAC9B,IAAI,cAAc,GAAqB,EAAE,CAAC;QAC1C,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;YAC3B,MAAM,WAAW,GAAG,MAAM,iBAAiB,CAAC,UAAU,EAAE,SAAS,CAAC,CAAC;YACnE,cAAc,GAAG,WAAW,CAAC,OAAO,CAAC;QACvC,CAAC;QAED,yBAAyB;QACzB,MAAM,cAAc,GAAG,oBAAoB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEvE,oBAAoB;QACpB,MAAM,SAAS,GAAsB;YACnC,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC;YACvD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE;YACrD,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,IAAI;YAChB,YAAY,EAAE,IAAI;SACnB,CAAC,CAAC;QAEH,0BAA0B;QAC1B,MAAM,cAAc,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAEvD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YAChC,MAAM,EAAE,cAAc;YACtB,UAAU,EAAE,cAAc,CAAC,cAAc,CAAC;SAC3C,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAC9E,QAAQ,CAAC,uBAAuB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAE5F,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YAChC,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,YAAY;SACpB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAClC,SAAiB,EACjB,SAAoB,EACpB,UAAkB,EAClB,OAA0B,EAC1B,cAAuB;IAEvB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,CAAC;QACH,OAAO,CAAC,2BAA2B,CAAC,CAAC;QAErC,0BAA0B;QAC1B,MAAM,eAAe,GAAG,qBAAqB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QAEzE,oBAAoB;QACpB,MAAM,SAAS,GAAsB;YACnC,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC;YACxD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,eAAe;QACf,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE;YACrD,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;SACpB,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,eAAe,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAExD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YAChC,MAAM,EAAE,eAAe;YACvB,UAAU,EAAE,cAAc,CAAC,eAAe,CAAC;SAC5C,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAC9E,QAAQ,CAAC,wBAAwB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAE7F,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YAChC,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,YAAY;SACpB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CACnC,SAAiB,EACjB,SAAoB,EACpB,UAAkB,EAClB,OAA0B,EAC1B,eAAwB;IAExB,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,IAAI,CAAC;QACH,OAAO,CAAC,4BAA4B,CAAC,CAAC;QAEtC,2BAA2B;QAC3B,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,SAAS,EAAE,eAAe,CAAC,CAAC;QAE5E,oBAAoB;QACpB,MAAM,SAAS,GAAsB;YACnC,IAAI,EAAE,OAAO;YACb,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,IAAI,EAAE,gBAAgB,EAAE,CAAC;YACzD,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;SACtB,CAAC;QAEF,qBAAqB;QACrB,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,WAAW,CAAC,SAAS,EAAE;YACrD,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,IAAI;YAChB,cAAc,EAAE,IAAI;SACrB,CAAC,CAAC;QAEH,4BAA4B;QAC5B,MAAM,gBAAgB,GAAG,sBAAsB,CAAC,OAAO,CAAC,CAAC;QAEzD,OAAO;YACL,OAAO,EAAE,IAAI;YACb,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YAChC,MAAM,EAAE,gBAAgB;YACxB,UAAU,EAAE,cAAc,CAAC,gBAAgB,CAAC;SAC7C,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,eAAe,CAAC;QAC9E,QAAQ,CAAC,yBAAyB,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;QAE9F,OAAO;YACL,OAAO,EAAE,KAAK;YACd,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YAChC,MAAM,EAAE,EAAE;YACV,UAAU,EAAE,CAAC;YACb,KAAK,EAAE,YAAY;SACpB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,SAAiB,EAAE,cAAgC;IAC/E,MAAM,WAAW,GAAG,cAAc;SAC/B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC;SACpC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAS,CAAC,IAAI,CAAC;SACzB,IAAI,CAAC,IAAI,CAAC,CAAC;IAEd,OAAO;QACL,qCAAqC;QACrC,EAAE;QACF,SAAS;QACT,SAAS;QACT,EAAE;QACF,YAAY;QACZ,WAAW,IAAI,iCAAiC;QAChD,EAAE;QACF,iBAAiB;QACjB,qEAAqE;QACrE,yDAAyD;QACzD,6EAA6E;QAC7E,4EAA4E;QAC5E,EAAE;QACF,yBAAyB;QACzB,uDAAuD;QACvD,sDAAsD;QACtD,8DAA8D;QAC9D,4DAA4D;QAC5D,mDAAmD;QACnD,0CAA0C;QAC1C,0DAA0D;QAC1D,EAAE;QACF,iCAAiC;KAClC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,qBAAqB,CAAC,SAAiB,EAAE,cAAuB;IACvE,OAAO;QACL,sCAAsC;QACtC,EAAE;QACF,kBAAkB;QAClB,SAAS;QACT,EAAE;QACF,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE;QAC/B,cAAc,IAAI,EAAE;QACpB,EAAE;QACF,iBAAiB;QACjB,+FAA+F;QAC/F,8EAA8E;QAC9E,kDAAkD;QAClD,sDAAsD;QACtD,EAAE;QACF,uBAAuB;KACxB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,SAAiB,EAAE,eAAwB;IACzE,OAAO;QACL,uCAAuC;QACvC,EAAE;QACF,kBAAkB;QAClB,SAAS;QACT,EAAE;QACF,qBAAqB;QACrB,eAAe,IAAI,+BAA+B;QAClD,EAAE;QACF,iBAAiB;QACjB,yDAAyD;QACzD,YAAY;QACZ,wDAAwD;QACxD,mDAAmD;QACnD,iDAAiD;QACjD,6CAA6C;QAC7C,EAAE;QACF,qCAAqC;KACtC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,sBAAsB,CAAC,OAAuB;IACrD,MAAM,SAAS,GAAa,EAAE,CAAC;IAE/B,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC3B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;YAChE,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO;iBAC7B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,YAAY,CAAC;iBACpC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAS,CAAC,IAAI,CAAC;iBACzB,IAAI,CAAC,IAAI,CAAC,CAAC;YACd,IAAI,WAAW,EAAE,CAAC;gBAChB,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAChC,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB,CAAC,eAAuB;IAC5D,MAAM,KAAK,GAAmB,EAAE,CAAC;IAEjC,qBAAqB;IACrB,KAAK,CAAC,IAAI,CAAC;QACT,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,eAAe;QACxB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;QACrB,QAAQ,EAAE;YACR,KAAK,EAAE,aAAa;YACpB,QAAQ,EAAE,aAAa;SACxB;KACF,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;GAEG;AACH,SAAS,cAAc,CAAC,IAAY;IAClC,8CAA8C;IAC9C,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AACpC,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,oBAAoB,CAAC,SAAiB;IAKpD,MAAM,OAAO,GAAa,EAAE,CAAC;IAC7B,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,sCAAsC;IACtC,MAAM,gBAAgB,GAAG;QACvB,sCAAsC;QACtC,mCAAmC;QACnC,+BAA+B;QAC/B,4BAA4B;KAC7B,CAAC;IAEF,2CAA2C;IAC3C,MAAM,oBAAoB,GAAG;QAC3B,0CAA0C;QAC1C,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;KACvC,CAAC;IAEF,0BAA0B;IAC1B,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE,CAAC;QACvC,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,KAAK,IAAI,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;YACpE,MAAM;QACR,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;QAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YAC5B,KAAK,IAAI,EAAE,CAAC;YACZ,OAAO,CAAC,IAAI,CAAC,uCAAuC,CAAC,CAAC;YACtD,MAAM;QACR,CAAC;IACH,CAAC;IAED,gBAAgB;IAChB,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAC3B,KAAK,IAAI,EAAE,CAAC;QACZ,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC9C,CAAC;SAAM,IAAI,SAAS,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAClC,KAAK,IAAI,EAAE,CAAC;QACZ,OAAO,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;IAC9C,CAAC;IAED,wBAAwB;IACxB,MAAM,QAAQ,GAAG,KAAK,IAAI,EAAE,CAAC;IAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC;IAE1D,IAAI,QAAQ,EAAE,CAAC;QACb,OAAO,CAAC,IAAI,CAAC,6CAA6C,CAAC,CAAC;IAC9D,CAAC;SAAM,CAAC;QACN,OAAO,CAAC,IAAI,CAAC,iDAAiD,CAAC,CAAC;IAClE,CAAC;IAED,OAAO;QACL,QAAQ;QACR,OAAO;QACP,UAAU;KACX,CAAC;AACJ,CAAC"}