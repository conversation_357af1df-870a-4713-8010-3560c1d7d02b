/**
 * Agent State Management Hook
 *
 * Provides centralized state management for agent operations
 * Handles execution state, progress tracking, and error management
 */
import type { AgentState, AgentOperation, AgentError } from '../types/index.js';
export interface UseAgentStateOptions {
    onStateChange?: (state: AgentState) => void;
    onError?: (error: AgentError) => void;
    onComplete?: (result: any) => void;
}
export interface AgentStateManager {
    state: AgentState;
    isIdle: boolean;
    isThinking: boolean;
    isExecuting: boolean;
    isStreaming: boolean;
    hasError: boolean;
    currentOperation: AgentOperation | null;
    error: AgentError | null;
    progress: number;
    startThinking: (operation?: AgentOperation) => void;
    startExecuting: (operation: AgentOperation) => void;
    startStreaming: (operation?: AgentOperation) => void;
    setProgress: (progress: number) => void;
    setError: (error: AgentError) => void;
    complete: (result?: any) => void;
    reset: () => void;
    updateOperation: (updates: Partial<AgentOperation>) => void;
    addStep: (step: string) => void;
    completeStep: (stepIndex: number) => void;
}
export declare function useAgentState(options?: UseAgentStateOptions): AgentStateManager;
/**
 * Hook for managing multiple agent operations
 */
export interface UseAgentQueueOptions {
    maxConcurrent?: number;
    onQueueChange?: (queue: AgentOperation[]) => void;
}
export declare function useAgentQueue(options?: UseAgentQueueOptions): {
    queue: AgentOperation[];
    active: AgentOperation[];
    completed: AgentOperation[];
    addToQueue: (operation: AgentOperation) => void;
    removeFromQueue: (operationId: string) => void;
    startNext: () => AgentOperation | null;
    completeOperation: (operationId: string, result?: any) => void;
    failOperation: (operationId: string, error: AgentError) => void;
    clearCompleted: () => void;
    clearAll: () => void;
    totalPending: number;
    totalActive: number;
    totalCompleted: number;
    canStartNext: boolean;
};
/**
 * Hook for tracking agent performance metrics
 */
export declare function useAgentMetrics(): {
    metrics: {
        totalOperations: number;
        successfulOperations: number;
        failedOperations: number;
        averageExecutionTime: number;
        totalExecutionTime: number;
        operationsPerMinute: number;
        lastOperationTime: number;
    };
    recordOperation: (success: boolean, executionTime: number) => void;
    resetMetrics: () => void;
    successRate: number;
    failureRate: number;
};
//# sourceMappingURL=use-agent-state.d.ts.map