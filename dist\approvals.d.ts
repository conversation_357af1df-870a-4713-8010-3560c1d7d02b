/**
 * Security & Approval System
 *
 * Handles command approval workflow with different policies
 * Provides safety checks and user interaction for command execution
 */
import type { ApprovalPolicy, AppConfig } from './types/index.js';
export declare enum ReviewDecision {
    YES = "yes",
    NO_CONTINUE = "no_continue",
    NO_EXIT = "no_exit",
    ALWAYS = "always",
    EXPLAIN = "explain"
}
export interface ApprovalRequest {
    command: string[];
    workdir: string;
    explanation?: string;
    metadata?: {
        estimatedRisk: 'low' | 'medium' | 'high';
        affectedFiles?: string[];
        networkAccess?: boolean;
    };
}
export interface ApprovalResponse {
    decision: ReviewDecision;
    customMessage?: string;
    rememberChoice?: boolean;
}
/**
 * Check if command can be auto-approved based on policy
 */
export declare function canAutoApprove(command: string[], approvalPolicy: ApprovalPolicy, safeCommands?: string[]): boolean;
/**
 * Assess command risk level
 */
export declare function assessCommandRisk(command: string[], workdir: string, config: AppConfig): 'low' | 'medium' | 'high';
/**
 * Generate explanation for command
 */
export declare function generateCommandExplanation(command: string[], workdir: string): string;
/**
 * Validate command safety
 */
export declare function validateCommandSafety(command: string[], workdir: string, config: AppConfig): {
    safe: boolean;
    warnings: string[];
    blockers: string[];
};
/**
 * Format approval request for display
 */
export declare function formatApprovalRequest(request: ApprovalRequest): {
    title: string;
    description: string;
    details: string[];
    riskLevel: string;
};
/**
 * Create approval request from command
 */
export declare function createApprovalRequest(command: string[], workdir: string, config: AppConfig): ApprovalRequest;
/**
 * Process approval response
 */
export declare function processApprovalResponse(response: ApprovalResponse, request: ApprovalRequest): {
    approved: boolean;
    shouldContinue: boolean;
    shouldRemember: boolean;
    message?: string;
};
//# sourceMappingURL=approvals.d.ts.map