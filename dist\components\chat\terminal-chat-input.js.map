{"version": 3, "file": "terminal-chat-input.js", "sourceRoot": "", "sources": ["../../../src/components/chat/terminal-chat-input.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,OAAO,CAAC;AACxE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC;AAC1C,OAAO,UAAU,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,wBAAwB,EAAE,MAAM,6BAA6B,CAAC;AACvE,OAAO,EAAE,iBAAiB,EAAE,MAAM,wCAAwC,CAAC;AAC3E,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAkB/D,MAAM,UAAU,iBAAiB,CAAC,EAChC,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,GAAG,KAAK,EAChB,WAAW,GAAG,sBAAsB,EACpC,SAAS,GAAG,IAAI,EAChB,eAAe,GAAG,IAAI,EACC;IACvB,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAa,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;IACvE,MAAM,CAAC,WAAW,EAAE,cAAc,CAAC,GAAG,QAAQ,CAAW,EAAE,CAAC,CAAC;IAC7D,MAAM,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpE,MAAM,CAAC,kBAAkB,EAAE,qBAAqB,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;IAChE,MAAM,CAAC,YAAY,EAAE,eAAe,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;IACrD,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC,CAAC;IACvD,MAAM,aAAa,GAAG,MAAM,CAAoB,IAAI,CAAC,CAAC;IAEtD,yBAAyB;IACzB,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;YAC3B,aAAa,CAAC,OAAO,GAAG,IAAI,UAAU,EAAE,CAAC;YACzC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,mDAAmD;IACnD,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,aAAa,CAAC,OAAO,IAAI,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,KAAK,KAAK,EAAE,CAAC;YACvE,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IAEZ,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,QAAQ;YAAE,OAAO;QAErB,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,sBAAsB;QACtB,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,GAAG,CAAC,KAAK,IAAI,SAAS,EAAE,CAAC;gBAC3B,6CAA6C;gBAC7C,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;gBAC5B,WAAW,EAAE,CAAC;YAChB,CAAC;iBAAM,CAAC;gBACN,kBAAkB;gBAClB,YAAY,EAAE,CAAC;YACjB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,GAAG,EAAE,CAAC;YACZ,mBAAmB,EAAE,CAAC;YACtB,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC;YAChB,IAAI,kBAAkB,EAAE,CAAC;gBACvB,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;YAClB,IAAI,kBAAkB,EAAE,CAAC;gBACvB,mBAAmB,CAAC,CAAC,CAAC,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,eAAe,CAAC,CAAC,CAAC,CAAC;YACrB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,IAAI,kBAAkB,EAAE,CAAC;gBACvB,eAAe,EAAE,CAAC;YACpB,CAAC;iBAAM,CAAC;gBACN,cAAc;gBACd,UAAU,CAAC,KAAK,EAAE,CAAC;gBACnB,WAAW,EAAE,CAAC;gBACd,YAAY,EAAE,CAAC;YACjB,CAAC;YACD,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAChC,IAAI,GAAG,CAAC,SAAS,EAAE,CAAC;gBAClB,UAAU,CAAC,SAAS,EAAE,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,MAAM,EAAE,CAAC;YACtB,CAAC;YACD,WAAW,EAAE,CAAC;YACd,iBAAiB,EAAE,CAAC;YACpB,OAAO;QACT,CAAC;QAED,iCAAiC;QACjC,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACpC,UAAU,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC;YAC7B,WAAW,EAAE,CAAC;YACd,iBAAiB,EAAE,CAAC;QACtB,CAAC;QAED,6BAA6B;QAC7B,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,UAAU,CAAC,SAAS,EAAE,CAAC;YACvB,WAAW,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QAED,uBAAuB;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,iDAAiD;YACjD,MAAM,YAAY,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;YAClD,IAAI,YAAY,EAAE,CAAC;gBACjB,yEAAyE;gBACzE,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YACvC,CAAC;YACD,OAAO;QACT,CAAC;QAED,wBAAwB;QACxB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,sCAAsC;YACtC,yEAAyE;YACzE,OAAO;QACT,CAAC;QAED,uBAAuB;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,UAAU,CAAC,IAAI,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,OAAO;QACT,CAAC;QAED,uBAAuB;QACvB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,UAAU,CAAC,IAAI,EAAE,CAAC;YAClB,WAAW,EAAE,CAAC;YACd,OAAO;QACT,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,WAAW,GAAG,WAAW,CAAC,GAAG,EAAE;QACnC,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;QACzC,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC;YACtC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACrB,CAAC;IACH,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAClC,IAAI,YAAY,EAAE,CAAC;YACjB,QAAQ,CAAC,YAAY,CAAC,CAAC;YACvB,+BAA+B;YAC/B,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;YAC/B,WAAW,EAAE,CAAC;YACd,YAAY,EAAE,CAAC;YACf,eAAe,EAAE,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC;IAEnC;;OAEG;IACH,MAAM,mBAAmB,GAAG,WAAW,CAAC,GAAG,EAAE;QAC3C,IAAI,kBAAkB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjD,4BAA4B;YAC5B,MAAM,UAAU,GAAG,WAAW,CAAC,kBAAkB,CAAC,CAAC;YACnD,eAAe,CAAC,UAAU,CAAC,CAAC;QAC9B,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,iBAAiB,EAAE,CAAC;QACtB,CAAC;IACH,CAAC,EAAE,CAAC,kBAAkB,EAAE,WAAW,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAE1D;;OAEG;IACH,MAAM,iBAAiB,GAAG,WAAW,CAAC,GAAG,EAAE;QACzC,IAAI,CAAC,eAAe;YAAE,OAAO;QAE7B,MAAM,YAAY,GAAG,KAAK,CAAC;QAC3B,MAAM,cAAc,GAAG,aAAa,CAAC,OAAO,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAExF,6CAA6C;QAC7C,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,KAAK,GAAG,YAAY,CAAC,SAAS,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;YAElD,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrB,MAAM,eAAe,GAAG,wBAAwB,CAAC,KAAK,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;gBACvE,cAAc,CAAC,eAAe,CAAC,CAAC;gBAChC,qBAAqB,CAAC,eAAe,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;gBAClD,qBAAqB,CAAC,CAAC,CAAC,CAAC;gBACzB,OAAO;YACT,CAAC;QACH,CAAC;QAED,sCAAsC;QACtC,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,kBAAkB,GAAG,cAAc;iBACtC,MAAM,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;iBACnD,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YAE3B,cAAc,CAAC,kBAAkB,CAAC,CAAC;YACnC,qBAAqB,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACrD,qBAAqB,CAAC,CAAC,CAAC,CAAC;YACzB,OAAO;QACT,CAAC;QAED,iCAAiC;QACjC,eAAe,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,KAAK,EAAE,eAAe,CAAC,CAAC,CAAC;IAE7B;;OAEG;IACH,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,UAAkB,EAAE,EAAE;QACzD,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;QACzC,IAAI,CAAC,UAAU;YAAE,OAAO;QAExB,MAAM,YAAY,GAAG,KAAK,CAAC;QAE3B,IAAI,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC/B,0BAA0B;YAC1B,MAAM,OAAO,GAAG,YAAY,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YAC9C,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS,CAAC,CAAC,EAAE,OAAO,GAAG,CAAC,CAAC,GAAG,UAAU,CAAC;YACrE,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,YAAY,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC;YACxC,wBAAwB;YACxB,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QACjC,CAAC;QAED,WAAW,EAAE,CAAC;QACd,eAAe,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;IAEzB;;OAEG;IACH,MAAM,mBAAmB,GAAG,WAAW,CAAC,CAAC,SAAiB,EAAE,EAAE;QAC5D,IAAI,CAAC,kBAAkB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE5D,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,kBAAkB,GAAG,SAAS,CAAC,CAAC,CAAC;QAC/F,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAClC,CAAC,EAAE,CAAC,kBAAkB,EAAE,WAAW,CAAC,MAAM,EAAE,kBAAkB,CAAC,CAAC,CAAC;IAEjE;;OAEG;IACH,MAAM,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;QACvC,qBAAqB,CAAC,KAAK,CAAC,CAAC;QAC7B,cAAc,CAAC,EAAE,CAAC,CAAC;QACnB,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC3B,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP;;OAEG;IACH,MAAM,eAAe,GAAG,WAAW,CAAC,CAAC,SAAiB,EAAE,EAAE;QACxD,MAAM,OAAO,GAAG,iBAAiB,EAAE,CAAC;QACpC,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEjC,IAAI,YAAY,KAAK,CAAC,CAAC,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;YAC5C,2CAA2C;YAC3C,gBAAgB,CAAC,KAAK,CAAC,CAAC;YACxB,eAAe,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACpC,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAChD,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACpD,WAAW,EAAE,CAAC;QAChB,CAAC;aAAM,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC;YAC7B,MAAM,QAAQ,GAAG,YAAY,GAAG,SAAS,CAAC;YAE1C,IAAI,QAAQ,IAAI,CAAC,IAAI,QAAQ,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;gBAC/C,eAAe,CAAC,QAAQ,CAAC,CAAC;gBAC1B,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;gBACtC,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;gBACpD,WAAW,EAAE,CAAC;YAChB,CAAC;iBAAM,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC;gBAC3B,yBAAyB;gBACzB,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;gBACpB,aAAa,CAAC,OAAO,EAAE,OAAO,CAAC,aAAa,CAAC,CAAC;gBAC9C,WAAW,EAAE,CAAC;YAChB,CAAC;QACH,CAAC;IACH,CAAC,EAAE,CAAC,YAAY,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,CAAC;IAEvC;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;QACpC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,+BAA+B;IAC/B,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,MAAM,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAChC,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC;QACzC,MAAM,SAAS,GAAG,UAAU,EAAE,iBAAiB,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;QAExE,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE;YACnC,IAAI,SAAS,KAAK,SAAS,CAAC,GAAG,EAAE,CAAC;gBAChC,mBAAmB;gBACnB,MAAM,YAAY,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC;gBACtD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;gBAElD,OAAO,CACL,MAAC,IAAI,eACF,YAAY,EACb,KAAC,IAAI,IAAC,eAAe,EAAC,OAAO,EAAC,KAAK,EAAC,OAAO,uBAAS,EACnD,WAAW,KAHH,SAAS,CAIb,CACR,CAAC;YACJ,CAAC;iBAAM,CAAC;gBACN,OAAO,KAAC,IAAI,cAAkB,IAAI,IAAhB,SAAS,CAAe,CAAC;YAC7C,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,aAEzB,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAChE,KAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,YACrC,KAAK,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,YAAE,WAAW,GAAQ,GAC5D,GACF,EAGL,kBAAkB,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,CAC/C,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,YACjD,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,KAAK,EAAC,MAAM,aACtC,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,mCAAoB,EAC1C,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,KAAK,EAAE,EAAE,CAAC,CAClD,KAAC,IAAI,IAEH,eAAe,EAAE,KAAK,KAAK,kBAAkB,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,EAClE,KAAK,EAAE,KAAK,KAAK,kBAAkB,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,YAErD,UAAU,IAJN,KAAK,CAKL,CACR,CAAC,IACE,GACF,CACP,IACG,CACP,CAAC;AACJ,CAAC"}