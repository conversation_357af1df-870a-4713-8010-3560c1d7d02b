/**
 * Command History Management System
 *
 * Provides persistent command history with security features
 * Includes sensitive data filtering and configurable storage
 */
import type { HistoryEntry } from '../../types/index.js';
export interface HistoryConfig {
    maxSize: number;
    saveHistory: boolean;
    sensitivePatterns: string[];
}
/**
 * Add command to history
 */
export declare function addToHistory(command: string, success?: boolean): void;
/**
 * Get command history
 */
export declare function getHistory(): HistoryEntry[];
/**
 * Get command history (alias for getHistory)
 */
export declare function getCommandHistory(): HistoryEntry[];
/**
 * Search command history
 */
export declare function searchHistory(query: string): HistoryEntry[];
/**
 * Get recent commands
 */
export declare function getRecentCommands(count?: number): HistoryEntry[];
/**
 * Get command at index (for navigation)
 */
export declare function getCommandAtIndex(index: number): string | null;
/**
 * Get history size
 */
export declare function getHistorySize(): number;
/**
 * Clear command history
 */
export declare function clearHistory(): void;
/**
 * Get history statistics
 */
export declare function getHistoryStats(): {
    totalCommands: number;
    uniqueCommands: number;
    successfulCommands: number;
    failedCommands: number;
    oldestCommand?: Date;
    newestCommand?: Date;
};
/**
 * Export history to file
 */
export declare function exportHistory(outputPath: string): boolean;
/**
 * Import history from file
 */
export declare function importHistory(inputPath: string, merge?: boolean): boolean;
/**
 * Update history configuration
 */
export declare function updateConfig(newConfig: Partial<HistoryConfig>): void;
/**
 * Get navigation history for arrow key navigation
 */
export declare class HistoryNavigator {
    private currentIndex;
    private history;
    constructor();
    /**
     * Get previous command
     */
    getPrevious(): string | null;
    /**
     * Get next command
     */
    getNext(): string | null;
    /**
     * Reset navigation
     */
    reset(): void;
    /**
     * Refresh history
     */
    refresh(): void;
}
//# sourceMappingURL=command-history.d.ts.map