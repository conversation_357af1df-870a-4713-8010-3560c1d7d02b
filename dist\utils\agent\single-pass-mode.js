/**
 * Single-Pass Mode Implementation
 *
 * Executes tasks in a single pass without iterative refinement
 * Optimized for speed and efficiency with comprehensive planning
 */
import { logInfo, logError } from '../logger/log.js';
import { gatherFullContext } from './full-context-mode.js';
const DEFAULT_OPTIONS = {
    useFullContext: true,
    maxTokens: 4000,
    temperature: 0.1, // Lower temperature for more deterministic results
    includeSystemPrompt: true,
    planningPhase: true,
    executionPhase: true,
    validationPhase: false // Optional validation phase
};
/**
 * Execute task in single-pass mode
 */
export async function executeSinglePass(userInput, agentLoop, workingDir = process.cwd(), options = {}) {
    const finalOptions = { ...DEFAULT_OPTIONS, ...options };
    const startTime = Date.now();
    logInfo('Starting single-pass execution', {
        userInput: userInput.substring(0, 100),
        workingDir,
        options: finalOptions
    });
    const result = {
        success: false,
        items: [],
        executionTime: 0,
        tokensUsed: 0,
        phases: {},
        warnings: [],
        errors: []
    };
    try {
        // Phase 1: Planning (if enabled)
        if (finalOptions.planningPhase) {
            const planningResult = await executePlanningPhase(userInput, agentLoop, workingDir, finalOptions);
            result.phases.planning = planningResult;
            result.tokensUsed += planningResult.tokensUsed;
            if (!planningResult.success) {
                result.errors.push(`Planning phase failed: ${planningResult.error}`);
                return result;
            }
        }
        // Phase 2: Execution
        if (finalOptions.executionPhase) {
            const executionResult = await executeExecutionPhase(userInput, agentLoop, workingDir, finalOptions, result.phases.planning?.output);
            result.phases.execution = executionResult;
            result.tokensUsed += executionResult.tokensUsed;
            result.items = await convertExecutionToItems(executionResult.output);
            if (!executionResult.success) {
                result.errors.push(`Execution phase failed: ${executionResult.error}`);
                return result;
            }
        }
        // Phase 3: Validation (if enabled)
        if (finalOptions.validationPhase) {
            const validationResult = await executeValidationPhase(userInput, agentLoop, workingDir, finalOptions, result.phases.execution?.output);
            result.phases.validation = validationResult;
            result.tokensUsed += validationResult.tokensUsed;
            if (!validationResult.success) {
                result.warnings.push(`Validation phase failed: ${validationResult.error}`);
            }
        }
        result.success = true;
        result.executionTime = Date.now() - startTime;
        logInfo('Single-pass execution completed', {
            success: result.success,
            executionTime: result.executionTime,
            tokensUsed: result.tokensUsed,
            phases: Object.keys(result.phases),
            items: result.items.length
        });
        return result;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Single-pass execution failed', error instanceof Error ? error : new Error(errorMessage));
        result.success = false;
        result.executionTime = Date.now() - startTime;
        result.errors.push(`Single-pass execution failed: ${errorMessage}`);
        return result;
    }
}
/**
 * Execute planning phase
 */
async function executePlanningPhase(userInput, agentLoop, workingDir, options) {
    const startTime = Date.now();
    try {
        logInfo('Executing planning phase');
        // Gather context if requested
        let contextContent = [];
        if (options.useFullContext) {
            const fullContext = await gatherFullContext(workingDir, userInput);
            contextContent = fullContext.content;
        }
        // Create planning prompt
        const planningPrompt = createPlanningPrompt(userInput, contextContent);
        // Create input item
        const inputItem = {
            type: 'input',
            role: 'user',
            content: [{ type: 'input_text', text: planningPrompt }],
            timestamp: Date.now()
        };
        // Execute planning
        const results = await agentLoop.executeLoop(inputItem, {
            maxIterations: 1,
            singlePass: true,
            planningMode: true
        });
        // Extract planning output
        const planningOutput = extractTextFromResults(results);
        return {
            success: true,
            duration: Date.now() - startTime,
            output: planningOutput,
            tokensUsed: estimateTokens(planningOutput)
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Planning phase failed', error instanceof Error ? error : new Error(errorMessage));
        return {
            success: false,
            duration: Date.now() - startTime,
            output: '',
            tokensUsed: 0,
            error: errorMessage
        };
    }
}
/**
 * Execute execution phase
 */
async function executeExecutionPhase(userInput, agentLoop, workingDir, options, planningOutput) {
    const startTime = Date.now();
    try {
        logInfo('Executing execution phase');
        // Create execution prompt
        const executionPrompt = createExecutionPrompt(userInput, planningOutput);
        // Create input item
        const inputItem = {
            type: 'input',
            role: 'user',
            content: [{ type: 'input_text', text: executionPrompt }],
            timestamp: Date.now()
        };
        // Execute task
        const results = await agentLoop.executeLoop(inputItem, {
            maxIterations: 1,
            singlePass: true,
            executionMode: true
        });
        // Extract execution output
        const executionOutput = extractTextFromResults(results);
        return {
            success: true,
            duration: Date.now() - startTime,
            output: executionOutput,
            tokensUsed: estimateTokens(executionOutput)
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Execution phase failed', error instanceof Error ? error : new Error(errorMessage));
        return {
            success: false,
            duration: Date.now() - startTime,
            output: '',
            tokensUsed: 0,
            error: errorMessage
        };
    }
}
/**
 * Execute validation phase
 */
async function executeValidationPhase(userInput, agentLoop, workingDir, options, executionOutput) {
    const startTime = Date.now();
    try {
        logInfo('Executing validation phase');
        // Create validation prompt
        const validationPrompt = createValidationPrompt(userInput, executionOutput);
        // Create input item
        const inputItem = {
            type: 'input',
            role: 'user',
            content: [{ type: 'input_text', text: validationPrompt }],
            timestamp: Date.now()
        };
        // Execute validation
        const results = await agentLoop.executeLoop(inputItem, {
            maxIterations: 1,
            singlePass: true,
            validationMode: true
        });
        // Extract validation output
        const validationOutput = extractTextFromResults(results);
        return {
            success: true,
            duration: Date.now() - startTime,
            output: validationOutput,
            tokensUsed: estimateTokens(validationOutput)
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Validation phase failed', error instanceof Error ? error : new Error(errorMessage));
        return {
            success: false,
            duration: Date.now() - startTime,
            output: '',
            tokensUsed: 0,
            error: errorMessage
        };
    }
}
/**
 * Create planning prompt
 */
function createPlanningPrompt(userInput, contextContent) {
    const contextText = contextContent
        .filter(c => c.type === 'input_text')
        .map(c => c.text)
        .join('\n');
    return [
        '# PLANNING PHASE - Single Pass Mode',
        '',
        '## Task',
        userInput,
        '',
        '## Context',
        contextText || 'No additional context provided.',
        '',
        '## Instructions',
        'Create a comprehensive, detailed plan to accomplish the given task.',
        'Your plan should be specific, actionable, and complete.',
        'Include all necessary steps, commands, file operations, and considerations.',
        'This is a single-pass execution, so the plan must be thorough and correct.',
        '',
        '## Required Plan Format',
        '1. **Analysis**: Understand the task and requirements',
        '2. **Approach**: High-level strategy and methodology',
        '3. **Steps**: Detailed, numbered steps with specific actions',
        '4. **Commands**: Exact commands to execute (if applicable)',
        '5. **Files**: Files to create, modify, or analyze',
        '6. **Validation**: How to verify success',
        '7. **Risks**: Potential issues and mitigation strategies',
        '',
        'Provide your complete plan now:'
    ].join('\n');
}
/**
 * Create execution prompt
 */
function createExecutionPrompt(userInput, planningOutput) {
    return [
        '# EXECUTION PHASE - Single Pass Mode',
        '',
        '## Original Task',
        userInput,
        '',
        planningOutput ? '## Plan' : '',
        planningOutput || '',
        '',
        '## Instructions',
        'Execute the task based on the plan above (if provided) or directly from the task description.',
        'This is a single-pass execution - complete the entire task in this response.',
        'Use all available tools and functions as needed.',
        'Provide clear output and explanations for each step.',
        '',
        'Execute the task now:'
    ].join('\n');
}
/**
 * Create validation prompt
 */
function createValidationPrompt(userInput, executionOutput) {
    return [
        '# VALIDATION PHASE - Single Pass Mode',
        '',
        '## Original Task',
        userInput,
        '',
        '## Execution Output',
        executionOutput || 'No execution output provided.',
        '',
        '## Instructions',
        'Validate that the task has been completed successfully.',
        'Check for:',
        '- Task completion: Was the original request fulfilled?',
        '- Quality: Is the output correct and well-formed?',
        '- Completeness: Are all requirements addressed?',
        '- Errors: Are there any issues or mistakes?',
        '',
        'Provide your validation assessment:'
    ].join('\n');
}
/**
 * Extract text content from results
 */
function extractTextFromResults(results) {
    const textParts = [];
    for (const item of results) {
        if (item.type === 'output') {
            textParts.push(item.content);
        }
        else if (item.type === 'message' && item.role === 'assistant') {
            const textContent = item.content
                .filter(c => c.type === 'input_text')
                .map(c => c.text)
                .join('\n');
            if (textContent) {
                textParts.push(textContent);
            }
        }
    }
    return textParts.join('\n\n');
}
/**
 * Convert execution output to response items
 */
async function convertExecutionToItems(executionOutput) {
    const items = [];
    // Create output item
    items.push({
        type: 'output',
        content: executionOutput,
        timestamp: Date.now(),
        metadata: {
            model: 'single-pass',
            provider: 'single-pass'
        }
    });
    return items;
}
/**
 * Estimate token count (simple approximation)
 */
function estimateTokens(text) {
    // Rough approximation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4);
}
/**
 * Check if single-pass mode is suitable for the task
 */
export function isSinglePassSuitable(userInput) {
    const reasons = [];
    let score = 0;
    // Factors that favor single-pass mode
    const favoringPatterns = [
        /^(create|generate|write|build|make)/i,
        /^(analyze|review|check|validate)/i,
        /^(explain|describe|document)/i,
        /^(list|show|display|find)/i
    ];
    // Factors that discourage single-pass mode
    const discouragingPatterns = [
        /\b(iterative|refine|improve|optimize)\b/i,
        /\b(complex|complicated|multi-step)\b/i,
        /\b(debug|troubleshoot|fix)\b/i,
        /\b(experiment|try|test different)\b/i
    ];
    // Check favoring patterns
    for (const pattern of favoringPatterns) {
        if (pattern.test(userInput)) {
            score += 20;
            reasons.push('Task appears to be straightforward and well-defined');
            break;
        }
    }
    // Check discouraging patterns
    for (const pattern of discouragingPatterns) {
        if (pattern.test(userInput)) {
            score -= 30;
            reasons.push('Task may require iterative refinement');
            break;
        }
    }
    // Length factor
    if (userInput.length < 100) {
        score += 10;
        reasons.push('Task description is concise');
    }
    else if (userInput.length > 500) {
        score -= 10;
        reasons.push('Task description is complex');
    }
    // Determine suitability
    const suitable = score >= 10;
    const confidence = Math.min(100, Math.max(0, score + 50));
    if (suitable) {
        reasons.push('Single-pass mode recommended for efficiency');
    }
    else {
        reasons.push('Interactive mode recommended for better results');
    }
    return {
        suitable,
        reasons,
        confidence
    };
}
//# sourceMappingURL=single-pass-mode.js.map