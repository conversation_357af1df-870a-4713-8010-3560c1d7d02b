/**
 * File Tag Expansion System
 *
 * Handles conversion between @file.txt tags and XML blocks
 * Provides bidirectional transformation for file content inclusion
 */
/**
 * Expand @file.txt tags to XML blocks with file contents
 */
export declare function expandFileTags(text: string, workingDir?: string): Promise<string>;
/**
 * Collapse XML blocks back to @file.txt format
 */
export declare function collapseXmlBlocks(text: string): string;
/**
 * Extract file paths from text
 */
export declare function extractFilePaths(text: string): string[];
/**
 * Validate file paths and return status
 */
export declare function validateFilePaths(filePaths: string[], workingDir?: string): {
    valid: Array<{
        path: string;
        size: number;
        type: 'text' | 'binary';
    }>;
    invalid: Array<{
        path: string;
        error: string;
    }>;
};
/**
 * Get file information without reading content
 */
export declare function getFileInfo(filePath: string, workingDir?: string): {
    exists: boolean;
    isFile?: boolean;
    size?: number;
    type?: 'text' | 'binary';
    error?: string;
};
/**
 * Preview file content (first few lines)
 */
export declare function previewFileContent(filePath: string, workingDir?: string, maxLines?: number): {
    success: boolean;
    preview?: string;
    totalLines?: number;
    error?: string;
};
//# sourceMappingURL=file-tag-utils.d.ts.map