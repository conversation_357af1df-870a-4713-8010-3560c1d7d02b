/**
 * Approval Mode Selection Component
 *
 * Interactive interface for selecting command approval policies
 * Provides detailed explanations and security warnings
 */
import type { ApprovalPolicy } from '../../types/index.js';
interface ApprovalSelectorProps {
    currentMode: ApprovalPolicy;
    onSelect: (mode: ApprovalPolicy) => void;
    onCancel: () => void;
}
export declare function ApprovalSelector({ currentMode, onSelect, onCancel }: ApprovalSelectorProps): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=approval-selector.d.ts.map