## Implementation Status Analysis

Based on the current codebase examination, here are the missing components that need to be implemented:

### ✅ Already Implemented:
- Basic CLI entry point (`src/cli.tsx`)
- Main application class (`src/app.tsx`)
- Core agent loop (`src/utils/agent/agent-loop.ts`)
- Terminal chat interface (`src/components/chat/terminal-chat.tsx`)
- Basic configuration system (`src/utils/config.ts`)
- Provider management (`src/utils/providers.ts`)
- Command history storage (`src/utils/storage/command-history.ts`)
- Session rollout storage (`src/utils/storage/save-rollout.ts`)
- Basic logging (`src/utils/logger/log.ts`)

### ❌ Missing Critical Components:

1. **Advanced UI Components & Overlays**
2. **Comprehensive Tool System & Sandbox Execution**
3. **Multi-Modal Input Processing**
4. **Advanced File Operations & Patch System**
5. **Security & Approval System**
6. **Full-Context & Single-Pass Mode**
7. **Advanced Input System with Text Buffer**
8. **Vendor Components & Third-Party Integration**
9. **Custom Hooks & UI Components**
10. **Bug Reporting & Telemetry System**

### Implementation Plan:
Implement all missing components with full functionality, proper integration, and real business logic without placeholders.



# Kritrima AI CLI - Comprehensive System Integration Plan 

## Table of Contents
1. [System Architecture Overview](#system-architecture-overview)
2. [Provider & LLM Integration System](#provider--llm-integration-system)
3. [Autonomous Agent Loop & Tool Calling](#autonomous-agent-loop--tool-calling with Comprehensive well designed and well instructed and well informative system prompt)
4. [Advanced Code Assistance System](#advanced-code-assistance-system)
5. [Multi-Modal AI Interaction](#multi-modal-ai-interaction)
6. [Configuration & State Management](#configuration--state-management)
7. [Security & Approval System](#security--approval-system)
8. [User Interface & Experience](#user-interface--experience)
9. [Data Flow & Integration Points](#data-flow--integration-points)
10. [Error Handling & Resilience](#error-handling--resilience)
11. [Advanced Storage & Session Management](#advanced-storage--session-management)
12. [Logging & Debugging Infrastructure](#logging--debugging-infrastructure)
13. [Bug Reporting & Telemetry System](#bug-reporting--telemetry-system)
14. [Full-Context & Single-Pass Mode](#full-context--single-pass-mode)
15. [Command History & User Experience](#command-history--user-experience)
16. [File Operations & Tag System](#file-operations--tag-system)
17. [Custom Hooks & UI Components](#custom-hooks--ui-components)
18. [Vendor Components & Third-Party Integration](#vendor-components--third-party-integration)
19. [Text Buffer & Advanced Input System](#text-buffer--advanced-input-system)
20. [Example Projects & Templates](#example-projects--templates)
21. [Advanced Features & Integrations](#advanced-features--integrations)

---

## System Architecture Overview

The Kritrima AI CLI is built as a sophisticated, multi-layered system with the following core components:

```
┌─────────────────────────────────────────────────────────────┐
│                    CLI Entry Point                          │
│                  (bin/kritrima-ai.js)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Main CLI Application                         │
│                   (src/cli.tsx)                            │
│  • Argument parsing • Config loading • Mode selection      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Application Layer                           │
│                  (src/app.tsx)                             │
│  • Git validation • Safety checks • UI orchestration      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              Terminal Chat Interface                        │
│           (src/components/chat/terminal-chat.tsx)          │
│  • Real-time conversation • Model switching • Overlays     │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Agent Loop Core                              │
│           (src/utils/agent/agent-loop.ts)                  │
│  • AI orchestration • Tool calling • State management      │
└─────────────────────────────────────────────────────────────┘
```

---

## Provider & LLM Integration System

### 1. Provider Configuration Architecture

**File: `src/utils/providers.ts`**
- Defines supported AI providers with their configurations
- Built-in providers: OpenAI, Azure, Gemini, Ollama, Mistral, DeepSeek, xAI, Groq, ArceeAI, OpenRouter
- Each provider has: `name`, `baseURL`, `envKey` for API authentication

```typescript
export const providers: Record<string, { name: string; baseURL: string; envKey: string }> = {
  openai: { name: "OpenAI", baseURL: "https://api.openai.com/v1", envKey: "OPENAI_API_KEY" },
  ollama: { name: "Ollama", baseURL: "http://localhost:11434/v1", envKey: "OLLAMA_API_KEY" },
  // ... other providers
};
```

### 2. Dynamic Provider Resolution

**File: `src/utils/config.ts`**
- `getBaseUrl()`: Resolves provider base URL with environment variable override support
- `getApiKey()`: Retrieves API keys with fallback mechanisms
- Supports custom providers via environment variables (`PROVIDER_API_KEY`, `PROVIDER_BASE_URL`)

### 3. OpenAI Client Factory

**File: `src/utils/openai-client.ts`**
- `createOpenAIClient()`: Creates provider-specific client instances
- Handles both standard OpenAI and Azure OpenAI configurations
- Applies timeout, organization, and project headers

### 4. Model Management System

**File: `src/utils/model-utils.ts`**
- `fetchModels()`: Dynamically retrieves available models from providers
- Background caching for performance
- Model validation and compatibility checking
- Context length calculation and management

**File: `src/utils/model-info.ts`**
- Comprehensive model metadata (context lengths, capabilities)
- Support for latest models (o1, o3, GPT-4.1, etc.)

### 5. Provider Switching Workflow

**Component: `src/components/model-overlay.tsx`**
1. User triggers model/provider overlay (`/model` command or hotkey)
2. System fetches available models for current provider
3. Tab navigation between provider and model selection
4. Real-time model list updates when provider changes
5. Configuration persistence and session management

---

## Autonomous Agent Loop & Tool Calling

### 1. Core Agent Architecture

**File: `src/utils/agent/agent-loop.ts` (1673 lines)**

The agent loop is the heart of the AI system, implementing a sophisticated autonomous workflow:

```typescript
export class AgentLoop {
  private model: string;
  private provider: string;
  private oai: OpenAI;
  private approvalPolicy: ApprovalPolicy;
  private transcript: Array<ResponseInputItem> = [];
  private pendingAborts: Set<string> = new Set();
}
```

### 2. Tool Calling System

#### Available Tools:
1. **Shell Tool** (`shell`): Execute system commands
2. **Local Shell Tool** (`local_shell`): Direct command execution
3. **Apply Patch Tool** (via shell): File modification operations

#### Tool Definition:
```typescript
const shellFunctionTool: FunctionTool = {
  type: "function",
  name: "shell",
  description: "Runs a shell command, and returns its output.",
  parameters: {
    type: "object",
    properties: {
      command: { type: "array", items: { type: "string" } },
      workdir: { type: "string" },
      timeout: { type: "number" }
    },
    required: ["command"]
  }
};
```

### 3. Agent Loop Execution Flow

#### Phase 1: Request Processing
1. **Input Validation**: Validate and sanitize user input
2. **Context Assembly**: Build conversation context with history
3. **Tool Registration**: Register available tools based on approval policy
4. **Request Preparation**: Format request for AI provider

#### Phase 2: AI Response Handling
1. **Streaming Response**: Process real-time AI responses
2. **Tool Call Detection**: Identify function calls in response
3. **Tool Call Parsing**: Extract command details and parameters
4. **Approval Workflow**: Route through approval system if required

#### Phase 3: Tool Execution
1. **Command Validation**: Security checks and approval
2. **Sandbox Execution**: Run commands in secure environment
3. **Output Capture**: Collect and format execution results
4. **Response Assembly**: Prepare results for next AI iteration

#### Phase 4: Conversation Continuation
1. **Context Update**: Add results to conversation history
2. **Loop Decision**: Determine if more iterations needed
3. **State Persistence**: Save conversation state
4. **UI Update**: Refresh interface with new information

### 4. Function Call Processing

**Method: `handleFunctionCall()`**
```typescript
private async handleFunctionCall(item: ResponseFunctionToolCall): Promise<Array<ResponseInputItem>> {
  const { name, arguments: argsString } = item;
  
  if (name === "shell" || name === "container.exec") {
    const { outputText, metadata, additionalItems } = await handleExecCommand(
      args, this.config, this.approvalPolicy, 
      this.additionalWritableRoots, this.getCommandConfirmation,
      this.execAbortController?.signal
    );
    return [outputItem, ...additionalItems];
  }
}
```

### 5. Conversation State Management

#### Server-Side Storage (Default):
- Uses `previous_response_id` for context continuity
- Efficient for long conversations
- Requires stable connection to AI provider

#### Client-Side Storage (Fallback):
- Maintains full transcript locally
- Sends complete context with each request
- Resilient to connection issues
- Higher token usage but more reliable

---

## Advanced Code Assistance System

### 1. File Operations System

**File: `src/utils/agent/apply-patch.ts` (816 lines)**

#### Unified Diff Processing:
- **Patch Parsing**: Parse unified diff format
- **File Creation**: Create new files with content
- **File Editing**: Apply incremental changes
- **File Deletion**: Remove files safely
- **Backup System**: Automatic backup before changes
- **Rollback Capability**: Undo changes if needed

#### V4A Diff Format:
```
*** [ACTION] File: [path/to/file]
[context_before]
- [old_code]
+ [new_code]
[context_after]
```

#### Conflict Resolution:
- Automatic merge conflict detection
- Interactive resolution prompts
- Safe fallback to manual editing

### 2. Shell Command Execution

**File: `src/utils/agent/handle-exec-command.ts` (378 lines)**

#### Command Processing Pipeline:
1. **Command Parsing**: Parse shell command syntax
2. **Platform Adaptation**: Convert Unix commands for Windows
3. **Security Validation**: Check against approval policies
4. **Working Directory**: Set execution context
5. **Timeout Management**: Prevent hanging processes
6. **Output Capture**: Stream stdout/stderr
7. **Result Formatting**: Structure output for AI consumption

#### Platform Command Adaptation:
**File: `src/utils/agent/platform-commands.ts`**
```typescript
const COMMAND_MAP: Record<string, string> = {
  ls: "dir",      // Unix to Windows
  grep: "findstr",
  cat: "type",
  rm: "del"
};
```

### 3. Git Integration

#### Git Repository Detection:
- **Safety Checks**: Warn when outside git repositories
- **Branch Awareness**: Understand current git state
- **Change Tracking**: Monitor file modifications
- **Diff Generation**: Create git diffs for review

#### Git Operations:
- Status checking
- Diff generation
- Branch information
- Commit history analysis

### 4. Project Documentation Analysis

**File: `src/utils/config.ts`**

#### Documentation Discovery:
```typescript
export function discoverProjectDocPath(startDir: string): string | null {
  const candidates = ["AGENTS.md", "README.md", "docs/README.md"];
  // Search upward through directory tree
}
```

#### Context Integration:
- Automatic inclusion of project documentation
- Custom documentation path support
- Markdown parsing and formatting
- Context-aware code suggestions

### 5. Code Generation & Debugging

#### Intelligent Code Generation:
- **Context-Aware**: Uses project structure and documentation
- **Language Detection**: Automatic programming language identification
- **Best Practices**: Follows established patterns in codebase
- **Incremental Building**: Builds upon existing code

#### Debugging Assistance:
- **Error Analysis**: Parse and understand error messages
- **Stack Trace Analysis**: Navigate through call stacks
- **Fix Suggestions**: Propose specific solutions
- **Testing Integration**: Generate and run tests

---

## Multi-Modal AI Interaction

### 1. Text and Image Input Support

**File: `src/utils/input-utils.ts`**

#### Input Processing:
```typescript
export async function createInputItem(
  text: string, 
  imagePaths: Array<string>
): Promise<ResponseInputItem> {
  const content: Array<ResponseContentInput> = [];
  
  if (text.trim()) {
    content.push({ type: "input_text", text });
  }
  
  for (const imagePath of imagePaths) {
    const imageData = await processImage(imagePath);
    content.push({ type: "input_image", image: imageData });
  }
  
  return { role: "user", content, type: "message" };
}
```

#### Image Processing:
- **Format Support**: PNG, JPEG, WebP, GIF
- **Size Optimization**: Automatic resizing for API limits
- **Base64 Encoding**: Convert to API-compatible format
- **Metadata Extraction**: Preserve relevant image information

### 2. Streaming Response Processing

**File: `src/utils/responses.ts` (718 lines)**

#### Real-Time Streaming:
```typescript
async function* streamResponses(
  input: ResponseCreateInput,
  completion: AsyncIterable<OpenAI.ChatCompletionChunk>
): AsyncGenerator<ResponseEvent> {
  for await (const chunk of completion) {
    if (chunk.choices?.[0]?.delta?.content) {
      yield {
        type: "response.output_text.delta",
        delta: chunk.choices[0].delta.content
      };
    }
  }
}
```

#### Event Types:
- `response.created`: Initial response
- `response.output_text.delta`: Incremental text
- `response.function_call_arguments.done`: Tool call completion
- `response.completed`: Final response

### 3. Context-Aware Conversations

#### Context Management:
- **Conversation History**: Maintain full dialogue context
- **File Context**: Include relevant file contents
- **Project Context**: Integrate project documentation
- **Command History**: Reference previous commands
- **Error Context**: Include error messages and stack traces

#### Context Optimization:
- **Token Management**: Monitor context length limits
- **Context Compaction**: Summarize old conversations
- **Selective Context**: Include only relevant information
- **Context Caching**: Optimize repeated context usage

### 4. Model Switching Integration

#### Dynamic Model Changes:
- **Session Continuity**: Maintain conversation across model switches
- **Context Transfer**: Preserve relevant context
- **Capability Adaptation**: Adjust features based on model capabilities
- **Performance Optimization**: Select optimal model for task

#### Provider Switching:
- **Seamless Transition**: Switch providers without losing context
- **API Compatibility**: Handle different API formats
- **Feature Mapping**: Adapt features to provider capabilities
- **Fallback Mechanisms**: Handle provider failures gracefully

---

## Configuration & State Management

### 1. Multi-Format Configuration Support

**File: `src/utils/config.ts` (598 lines)**

#### Configuration Hierarchy:
1. **Command Line Arguments**: Highest priority
2. **Environment Variables**: Override defaults
3. **Project Configuration**: Local `.kritrima-ai/config.json`
4. **User Configuration**: Global `~/.kritrima-ai/config.json`
5. **Default Values**: Built-in fallbacks

#### Supported Formats:
```typescript
// JSON Configuration
{
  "model": "gpt-4",
  "provider": "openai",
  "approvalMode": "suggest",
  "providers": { /* custom providers */ }
}

// YAML Configuration
model: gpt-4
provider: openai
approvalMode: suggest
providers:
  custom:
    name: "Custom Provider"
    baseURL: "https://api.custom.com"
    envKey: "CUSTOM_API_KEY"
```

### 2. Environment Variable Integration

#### Variable Resolution:
- `OPENAI_API_KEY`: Primary API key
- `PROVIDER_API_KEY`: Provider-specific keys
- `PROVIDER_BASE_URL`: Custom provider endpoints
- `KRITRIMA_AI_*`: Application-specific settings

#### Dynamic Loading:
```typescript
export function getApiKey(provider: string = "openai"): string | undefined {
  const config = loadConfig();
  const providerInfo = config.providers?.[provider.toLowerCase()];
  
  if (providerInfo) {
    return process.env[providerInfo.envKey];
  }
  
  // Fallback to custom environment variable
  return process.env[`${provider.toUpperCase()}_API_KEY`];
}
```

### 3. Provider Configuration System

#### Built-in Providers:
- OpenAI, Azure OpenAI, Gemini, Ollama
- Mistral, DeepSeek, xAI, Groq, ArceeAI, OpenRouter

#### Custom Provider Support:
```typescript
const customProviders = {
  myProvider: {
    name: "My Custom Provider",
    baseURL: "https://api.myprovider.com/v1",
    envKey: "MY_PROVIDER_API_KEY"
  }
};
```

### 4. Model Settings Management

#### Model Configuration:
- **Default Models**: Per-provider defaults
- **Context Limits**: Automatic context management
- **Capability Flags**: Feature availability per model
- **Performance Settings**: Timeout, retry, rate limiting

#### Dynamic Model Discovery:
```typescript
async function fetchModels(provider: string): Promise<Array<string>> {
  const openai = createOpenAIClient({ provider });
  const list = await openai.models.list();
  return models.sort();
}
```

### 5. Approval Policy System

#### Policy Types:
- **Suggest**: Manual approval for all actions
- **Auto-Edit**: Automatic file edits, manual commands
- **Full-Auto**: Automatic everything (with sandbox)

#### Policy Configuration:
```typescript
type ApprovalPolicy = "suggest" | "auto-edit" | "full-auto";

const policyConfig = {
  approvalMode: "suggest",
  safeCommands: ["ls", "cat", "grep"],
  dangerousCommands: ["rm", "sudo", "chmod"]
};
```

### 6. History Management

#### Command History:
- **Persistent Storage**: Save command history across sessions
- **Search Capability**: Find previous commands
- **Sensitive Filtering**: Remove sensitive data
- **Size Management**: Automatic cleanup of old entries

#### Session History:
- **Conversation Persistence**: Save full conversations
- **Session Resumption**: Continue previous sessions
- **Export/Import**: Share sessions between instances

---

## Security & Approval System

### 1. Sandbox Execution System

**Directory: `src/utils/agent/sandbox/`**

#### Platform-Specific Sandboxing:

##### Linux Landlock (`landlock.ts`):
```typescript
export async function execWithLandlock(
  input: ExecInput,
  config: AppConfig,
  additionalWritableRoots: ReadonlyArray<string>
): Promise<ExecResult> {
  // Restrict file system access using Linux Landlock LSM
  // Allow read access to project directory
  // Allow write access to specified writable roots
}
```

##### macOS Seatbelt (`macos-seatbelt.ts`):
```typescript
export async function execWithSeatbelt(
  input: ExecInput,
  config: AppConfig
): Promise<ExecResult> {
  // Use macOS Seatbelt for process sandboxing
  // Restrict network access, file system access
  // Allow only necessary system calls
}
```

##### Raw Execution (`raw-exec.ts`):
```typescript
export async function exec(
  input: ExecInput,
  config: AppConfig
): Promise<ExecResult> {
  // Fallback execution without sandboxing
  // Used when platform sandboxing unavailable
  // Still applies basic security checks
}
```

### 2. Command Approval Workflow

**File: `src/approvals.ts` (603 lines)**

#### Approval Decision Flow:
```typescript
export enum ReviewDecision {
  YES = "yes",
  NO_CONTINUE = "no_continue", 
  NO_EXIT = "no_exit",
  ALWAYS = "always",
  EXPLAIN = "explain"
}
```

#### Safety Checks:
1. **Command Validation**: Check against safe/dangerous command lists
2. **Path Validation**: Ensure operations within allowed directories
3. **Permission Checks**: Verify file system permissions
4. **Resource Limits**: Prevent resource exhaustion

#### Auto-Approval Logic:
```typescript
export function canAutoApprove(
  command: Array<string>,
  approvalPolicy: ApprovalPolicy,
  safeCommands: Array<string>
): boolean {
  if (approvalPolicy === "suggest") return false;
  if (approvalPolicy === "full-auto") return true;
  
  // Auto-edit: approve safe commands only
  return safeCommands.includes(command[0]);
}
```

### 3. File System Security

#### Path Resolution:
```typescript
export function resolvePathAgainstWorkdir(
  path: string,
  workdir: string
): string {
  // Prevent directory traversal attacks
  // Resolve relative paths safely
  // Validate against allowed directories
}
```

#### Permission Handling:
- **Read Permissions**: Validate before file access
- **Write Permissions**: Check before modifications
- **Execute Permissions**: Verify before command execution
- **Directory Permissions**: Ensure proper access rights

### 4. Network Security

#### API Security:
- **API Key Management**: Secure storage and transmission
- **Request Validation**: Sanitize all API requests
- **Response Validation**: Verify API responses
- **Rate Limiting**: Prevent API abuse

#### Proxy Support:
```typescript
const PROXY_URL = process.env["HTTPS_PROXY"];
const httpAgent = PROXY_URL ? new HttpsProxyAgent(PROXY_URL) : undefined;
```

---

## User Interface & Experience

### 1. Terminal Chat Interface

**File: `src/components/chat/terminal-chat.tsx` (767 lines)**

#### Core Features:
- **Real-time Conversation**: Live AI interaction with screen management
- **Model Selection**: Dynamic model/provider switching with overlay widgets
- **Approval Handling**: Interactive command approval using forms
- **Context Display**: Show conversation context usage with progress bars
- **Notification System**: Desktop notifications for responses

#### Screen Management:
```typescript
const screen = screen({
  smartCSR: true,
  title: 'Kritrima AI CLI'
});

const chatContainer = box({
  parent: screen,
  top: 0,
  left: 0,
  width: '100%',
  height: '100%-3',
  scrollable: true,
  alwaysScroll: true,
  border: {
    type: 'line'
  }
});
```

#### State Management:
```typescript
const [model, setModel] = useState<string>(config.model);
const [provider, setProvider] = useState<string>(config.provider);
const [items, setItems] = useState<Array<ResponseItem>>([]);
const [loading, setLoading] = useState<boolean>(false);
const [approvalPolicy, setApprovalPolicy] = useState<ApprovalPolicy>(initialApprovalPolicy);
```

### 2. Advanced Input System

**File: `src/components/chat/terminal-chat-input.tsx` (1018 lines)**

#### Input Features:
- **Multiline Text Widget**: Rich text input with textarea widget
- **Slash Commands**: Built-in command system with auto-completion (`/help`, `/model`, etc.)
- **File Suggestions**: `@` prefix for file path completion using list widget
- **History Navigation**: Arrow key navigation through command history
- **Tab Completion**: Auto-complete for files and commands

#### Input Widget Configuration:
```typescript
const inputBox = .textarea({
  parent: screen,
  bottom: 0,
  left: 0,
  width: '100%',
  height: 3,
  border: {
    type: 'line'
  },
  style: {
    fg: 'white',
    border: {
      fg: 'cyan'
    }
  },
  inputOnFocus: true,
  scrollable: true
});
```

#### Slash Command System:
```typescript
const SLASH_COMMANDS = [
  { command: "/help", description: "Show help information" },
  { command: "/model", description: "Switch AI model" },
  { command: "/history", description: "View command history" },
  { command: "/clear", description: "Clear conversation" },
  { command: "/compact", description: "Compress conversation context" }
];

// auto-completion integration
inputBox.on('keypress', (ch, key) => {
  if (key.name === 'tab') {
    handleAutoCompletion(inputBox.getValue());
  }
});
```

#### File System Integration:
```typescript
function updateFsSuggestions(txt: string) {
  const suggestions = getFileSystemSuggestions(txt);
  
  const suggestionList = .list({
    parent: screen,
    top: 'center',
    left: 'center',
    width: '50%',
    height: '50%',
    border: {
      type: 'line'
    },
    items: suggestions,
    keys: true,
    vi: true
  });
  
  setFsSuggestions(suggestions);
}
```

### 3. Overlay System

#### Available Overlays:
- **History Overlay** (`history-overlay.tsx`): Browse command history with list widget
- **Sessions Overlay** (`sessions-overlay.tsx`): Manage saved sessions with table
- **Model Overlay** (`model-overlay.tsx`): Select models and providers with form
- **Help Overlay** (`help-overlay.tsx`): Show help and shortcuts with box
- **Diff Overlay** (`diff-overlay.tsx`): View file differences with textbox
- **Approval Overlay** (`approval-mode-overlay.tsx`): Configure approval settings with radio buttons

#### Overlay Navigation:
```typescript
type OverlayModeType = "none" | "history" | "sessions" | "model" | "approval" | "help" | "diff";

const [overlayMode, setOverlayMode] = useState<OverlayModeType>("none");

// overlay creation
function createOverlay(type: OverlayModeType) {
  const overlay = .box({
    parent: screen,
    top: 'center',
    left: 'center',
    width: '80%',
    height: '80%',
    border: {
      type: 'line'
    },
    style: {
      border: {
        fg: 'cyan'
      }
    },
    hidden: false
  });
  
  return overlay;
}
```

#### Modal System:
```typescript
// History overlay implementation
const historyOverlay = .list({
  parent: screen,
  label: 'Command History',
  top: 'center',
  left: 'center',
  width: '70%',
  height: '60%',
  border: {
    type: 'line'
  },
  items: commandHistory,
  keys: true,
  vi: true,
  scrollable: true,
  style: {
    selected: {
      bg: 'blue'
    }
  }
});
```

### 4. Response Rendering

**File: `src/components/chat/terminal-chat-response-item.tsx` (361 lines)**

#### Message Types with Widgets:
- **Text Messages**: Formatted AI responses using text widgets
- **Tool Calls**: Command execution displays with log widgets
- **System Messages**: Status and error information with styled boxes
- **File Operations**: Patch application results with textbox

#### Syntax Highlighting:
```typescript
// Code block rendering
const codeBlock = .box({
  parent: chatContainer,
  content: highlightedCode,
  width: '100%',
  height: 'shrink',
  border: {
    type: 'line'
  },
  style: {
    border: {
      fg: 'yellow'
    }
  },
  scrollable: true
});
```

#### Advanced Rendering Features:
- **Code Blocks**: Language-specific highlighting with styling
- **Command Output**: Formatted terminal output with log widget
- **Diff Display**: Unified diff visualization with colored text
- **Error Messages**: Highlighted error information with message boxes

### 5. Event Handling System

#### Keyboard Navigation:
```typescript
// Global key handling for interface
screen.key(['escape', 'q', 'C-c'], (ch, key) => {
  if (overlayMode !== "none") {
    setOverlayMode("none");
    screen.render();
  } else {
    process.exit(0);
  }
});

// Vim-style navigation
screen.key(['j', 'down'], () => {
  chatContainer.scroll(1);
  screen.render();
});

screen.key(['k', 'up'], () => {
  chatContainer.scroll(-1);
  screen.render();
});
```

#### Focus Management:
```typescript
// Focus management for multiple widgets
const widgets = [inputBox, chatContainer, overlayWidget];
let currentFocus = 0;

screen.key(['tab'], () => {
  widgets[currentFocus].blur();
  currentFocus = (currentFocus + 1) % widgets.length;
  widgets[currentFocus].focus();
  screen.render();
});
```

### 6. Performance Optimizations

#### Screen Updates:
```typescript
// Efficient screen rendering
let renderScheduled = false;

function scheduleRender() {
  if (!renderScheduled) {
    renderScheduled = true;
    process.nextTick(() => {
      screen.render();
      renderScheduled = false;
    });
  }
}

// Batch updates for better performance
function updateChatContent(newItems: Array<ResponseItem>) {
  chatContainer.setContent(formatItems(newItems));
  scheduleRender();
}
```

#### Syntax Highlighting:
- **Code Blocks**: Language-specific highlighting
- **Command Output**: Formatted terminal output
- **Diff Display**: Unified diff visualization
- **Error Messages**: Highlighted error information

#### Memory Management:
```typescript
// Cleanup for long conversations
function cleanupOldMessages() {
  if (items.length > MAX_ITEMS) {
    const keepItems = items.slice(-MAX_ITEMS);
    setItems(keepItems);
    updateChatContent(keepItems);
  }
}
```

---

## Data Flow & Integration Points

### 1. Application Startup Flow

```
1. Binary Entry (bin/kritrima-ai.js)
   ↓
2. CLI Argument Parsing (src/cli.tsx)
   ↓
3. Configuration Loading (src/utils/config.ts)
   ↓
4. API Key Validation (src/utils/get-api-key.tsx)
   ↓
5. Application Initialization (src/app.tsx)
   ↓
6. Terminal Chat Setup (src/components/chat/terminal-chat.tsx)
   ↓
7. Agent Loop Creation (src/utils/agent/agent-loop.ts)
```

### 2. Conversation Flow

```
1. User Input (terminal-chat-input.tsx)
   ↓
2. Input Processing (src/utils/input-utils.ts)
   ↓
3. Agent Loop Execution (agent-loop.ts)
   ↓
4. AI Provider Request (src/utils/openai-client.ts)
   ↓
5. Response Streaming (src/utils/responses.ts)
   ↓
6. Tool Call Detection (agent-loop.ts)
   ↓
7. Command Approval (src/approvals.ts)
   ↓
8. Sandbox Execution (src/utils/agent/sandbox/)
   ↓
9. Result Processing (agent-loop.ts)
   ↓
10. UI Update (terminal-chat.tsx)
```

### 3. Configuration Integration

```
Environment Variables
   ↓
Command Line Arguments
   ↓
User Config (~/.kritrima-ai/config.json)
   ↓
Project Config (./.kritrima-ai/config.json)
   ↓
Runtime Configuration Object
   ↓
Component Props & State
```

### 4. File Operation Flow

```
1. AI Generates Patch Command
   ↓
2. Command Approval Check (src/approvals.ts)
   ↓
3. Patch Parsing (src/utils/agent/apply-patch.ts)
   ↓
4. File Backup Creation
   ↓
5. Patch Application
   ↓
6. Conflict Resolution (if needed)
   ↓
7. Result Validation
   ↓
8. UI Feedback
```

### 5. Provider Switching Flow

```
1. User Triggers Model Overlay (/model command)
   ↓
2. Fetch Available Models (src/utils/model-utils.ts)
   ↓
3. Display Provider/Model Selection
   ↓
4. User Selection
   ↓
5. Configuration Update (src/utils/config.ts)
   ↓
6. Agent Loop Reconfiguration
   ↓
7. Session State Update
   ↓
8. UI Refresh
```

---

## Error Handling & Resilience

### 1. Network Error Handling

#### Retry Logic:
```typescript
const MAX_RETRIES = 8;
const RATE_LIMIT_RETRY_WAIT_MS = 500;

for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {
  try {
    stream = await responseCall(params);
    break;
  } catch (err) {
    if (isRateLimitError(err)) {
      await sleep(RATE_LIMIT_RETRY_WAIT_MS * Math.pow(2, attempt));
      continue;
    }
    throw err;
  }
}
```

#### Error Types:
- **Rate Limit Errors**: Exponential backoff retry
- **Network Timeouts**: Configurable timeout with retry
- **API Errors**: Graceful degradation and user notification
- **Connection Errors**: Automatic reconnection attempts

### 2. Graceful Degradation

#### Fallback Mechanisms:
- **Provider Fallback**: Switch to backup provider on failure
- **Model Fallback**: Use alternative model if primary unavailable
- **Feature Degradation**: Disable advanced features if needed
- **Offline Mode**: Limited functionality without network

#### Error Recovery:
```typescript
try {
  await executeCommand(command);
} catch (error) {
  if (error instanceof NetworkError) {
    // Retry with exponential backoff
    return retryWithBackoff(() => executeCommand(command));
  } else if (error instanceof PermissionError) {
    // Request user approval
    return requestApproval(command);
  } else {
    // Log error and continue
    logError(error);
    return { success: false, error: error.message };
  }
}
```

### 3. State Recovery

#### Session Persistence:
- **Auto-save**: Automatic conversation saving
- **Crash Recovery**: Restore state after unexpected termination
- **Partial Recovery**: Recover what's possible from corrupted state
- **Backup Strategy**: Multiple backup points for critical data

#### Context Recovery:
```typescript
export function recoverContext(sessionId: string): Array<ResponseItem> {
  try {
    const savedSession = loadSession(sessionId);
    return validateAndRepairContext(savedSession.items);
  } catch (error) {
    logError("Context recovery failed", error);
    return [];
  }
}
```

### 4. User Experience Continuity

#### Interruption Handling:
- **Graceful Cancellation**: Clean termination of ongoing operations
- **State Preservation**: Maintain conversation state during interruptions
- **Resume Capability**: Continue from interruption point
- **Progress Indication**: Show operation progress and allow cancellation

#### Error Communication:
```typescript
function handleError(error: Error, context: string): void {
  const userMessage = formatErrorForUser(error);
  const technicalDetails = formatErrorForLogging(error);
  
  showUserNotification(userMessage);
  logError(technicalDetails, context);
  
  if (isRecoverableError(error)) {
    offerRecoveryOptions(error);
  }
}
```

---

## Advanced Storage & Session Management

### 1. Session Rollout System

**File: `src/utils/storage/save-rollout.ts` (53 lines)**

#### Automatic Session Persistence:
```typescript
export function saveRollout(
  sessionId: string,
  items: Array<ResponseItem>,
): void {
  // Best-effort async saving without blocking UI
  saveRolloutAsync(sessionId, items).catch(() => {});
}
```

#### Session Storage Structure:
- **Location**: `~/.codex/sessions/`
- **Format**: `rollout-YYYY-MM-DD-{sessionId}.json`
- **Content**: Full conversation history with metadata
- **Metadata**: Timestamp, session ID, model configuration

#### Session Recovery Features:
- **View Mode**: Browse previous sessions read-only
- **Resume Mode**: Continue previous conversations
- **Session Search**: Find sessions by content or metadata
- **Export/Import**: Share sessions between instances

### 2. Command History Management

**File: `src/utils/storage/command-history.ts` (140 lines)**

#### Persistent Command History:
```typescript
export interface HistoryEntry {
  command: string;
  timestamp: number;
}

export interface HistoryConfig {
  maxSize: number;
  saveHistory: boolean;
  sensitivePatterns: Array<string>;
}
```

#### Security Features:
- **Sensitive Pattern Detection**: Automatic filtering of API keys, passwords
- **Configurable Patterns**: User-defined sensitive data patterns
- **Safe Storage**: Encrypted storage for sensitive commands
- **History Cleanup**: Automatic removal of old entries

#### History Navigation:
- **Arrow Key Navigation**: Up/Down through command history
- **Search Functionality**: Find previous commands by content
- **Duplicate Prevention**: Avoid storing consecutive identical commands
- **Context Preservation**: Maintain history across sessions

### 3. Conversation Context Management

#### Server-Side vs Client-Side Storage:
```typescript
// Server-side (default): Uses previous_response_id for efficiency
private readonly disableResponseStorage: boolean;

// Client-side fallback: Full transcript with each request
private transcript: Array<ResponseInputItem> = [];
```

#### Context Optimization:
- **Token Management**: Monitor and optimize context usage
- **Context Compaction**: Intelligent summarization of long conversations
- **Selective Context**: Include only relevant conversation parts
- **Context Caching**: Optimize repeated context usage

---

## Logging & Debugging Infrastructure

### 1. Comprehensive Logging System

**File: `src/utils/logger/log.ts` (138 lines)**

#### Multi-Platform Logging:
```typescript
class AsyncLogger implements Logger {
  private queue: Array<string> = [];
  private isWriting: boolean = false;
  
  log(message: string): void {
    const entry = `[${now()}] ${message}\n`;
    this.queue.push(entry);
    this.maybeWrite();
  }
}
```

#### Platform-Specific Paths:
- **macOS/Windows**: `$TMPDIR/oai-codex/codex-cli-{timestamp}.log`
- **Linux**: `~/.local/oai-codex/codex-cli-{timestamp}.log`
- **Symlink**: `codex-cli-latest.log` for easy tailing

#### Debug Features:
- **Conditional Logging**: Only active when `DEBUG=1`
- **Async Queue**: Non-blocking log writes
- **Structured Messages**: Timestamped, formatted entries
- **Performance Monitoring**: FPS debugging for UI rendering

### 2. Development & Testing Support

#### Test Infrastructure:
```typescript
// Mock logger for tests
vi.mock("../src/utils/logger/log.js", () => ({
  __esModule: true,
  log: () => {},
  isLoggingEnabled: () => false,
}));
```

#### Debug Modes:
- **FPS Debugging**: Monitor UI rendering performance
- **Agent Tracing**: Track agent loop execution
- **Network Monitoring**: Log API requests and responses
- **Memory Profiling**: Track memory usage patterns

---

## Bug Reporting & Telemetry System

### 1. Automated Bug Report Generation

**File: `src/utils/bug-report.ts` (83 lines)**

#### GitHub Integration:
```typescript
export function buildBugReportUrl({
  items,
  cliVersion,
  model,
  platform,
}: {
  items: Array<ResponseItem | ResponseOutputItem>;
  cliVersion: string;
  model: string;
  platform: string;
}): string {
  // Pre-fills GitHub issue template with session data
}
```

#### Report Contents:
- **Session Summary**: User interactions and AI responses
- **System Information**: Platform, CLI version, model details
- **Error Context**: Stack traces, error messages
- **Reproduction Steps**: Automated step extraction from conversation

#### Slash Command Integration:
```typescript
// /bug command generates pre-filled GitHub issue URL
case "/bug":
  const url = buildBugReportUrl({
    items: items ?? [],
    cliVersion: CLI_VERSION,
    model: loadConfig().model ?? "unknown",
    platform: [os.platform(), os.arch(), os.release()]
  });
```

### 2. Performance Monitoring

#### Thinking Time Tracking:
```typescript
// Track AI processing time across sessions
private cumulativeThinkingMs = 0;
```

#### Metrics Collection:
- **Response Times**: Track API response latency
- **Token Usage**: Monitor context consumption
- **Error Rates**: Track failure patterns
- **User Interactions**: Analyze usage patterns

---

## Full-Context & Single-Pass Mode

### 1. Full-Context Processing

**File: `src/cli-singlepass.tsx` (29 lines)**

#### Experimental Mode:
```typescript
// --full-context flag enables directory-wide analysis
if (fullContextMode) {
  await runSinglePass({
    originalPrompt: prompt,
    config,
    rootPath: process.cwd(),
  });
}
```

#### Features:
- **Directory Loading**: Load entire codebase into context
- **Batch Processing**: Apply multiple changes in one pass
- **Context Analysis**: Understand project structure
- **Intelligent Editing**: Make coordinated changes across files

### 2. Single-Pass File Operations

**File: `src/utils/singlepass/file_ops.ts` (33 lines)**

#### File Operation Schema:
```typescript
export const FileOperationSchema = z.object({
  path: z.string(),
  updated_full_content: z.string().nullable().optional(),
  delete: z.boolean().nullable().optional(),
  move_to: z.string().nullable().optional(),
});
```

#### Operation Types:
- **Full Content Updates**: Complete file replacements
- **File Deletion**: Safe file removal
- **File Movement**: Rename and move operations
- **Atomic Operations**: All-or-nothing change application

### 3. Context Limit Management

**File: `src/utils/singlepass/context_limit.ts` (56 lines)**

#### Size Calculation:
```typescript
export function computeSizeMap(
  root: string,
  files: Array<FileContent>,
): [Record<string, number>, Record<string, number>] {
  // Calculate file and directory sizes for context optimization
}
```

#### Optimization Strategies:
- **Size-Based Filtering**: Exclude large files from context
- **Directory Prioritization**: Focus on important directories
- **Content Summarization**: Compress large files
- **Incremental Loading**: Load files on demand

---

## Command History & User Experience

### 1. Advanced Input System

**File: `src/components/chat/terminal-chat-input.tsx` (1018 lines)**

#### Multiline Editor Features:
```typescript
const [editorState, setEditorState] = useState<{
  key: number;
  initialCursorOffset?: number;
}>({ key: 0 });
```

#### Input Capabilities:
- **Multiline Editing**: Rich text input with cursor management
- **History Navigation**: Arrow key navigation through commands
- **Tab Completion**: Auto-complete for files and commands
- **Slash Commands**: Built-in command system
- **File Suggestions**: `@` prefix for file path completion

### 2. File System Integration

#### File Tag System:
```typescript
// @file.txt expands to XML blocks with file contents
const expandedText = await expandFileTags(text);
```

#### Suggestion Engine:
- **Path Completion**: Intelligent file path suggestions
- **Context-Aware**: Suggestions based on current directory
- **Fuzzy Matching**: Flexible file name matching
- **Recent Files**: Prioritize recently accessed files

### 3. Slash Command System

**File: `src/utils/slash-commands.ts` (37 lines)**

#### Available Commands:
```typescript
export const SLASH_COMMANDS: Array<SlashCommand> = [
  { command: "/clear", description: "Clear conversation history" },
  { command: "/compact", description: "Compress conversation context" },
  { command: "/history", description: "Open command history" },
  { command: "/sessions", description: "Browse previous sessions" },
  { command: "/model", description: "Open model selection panel" },
  { command: "/approval", description: "Open approval mode selection" },
  { command: "/bug", description: "Generate bug report URL" },
  { command: "/diff", description: "Show git diff" },
];
```

#### Command Processing:
- **Auto-completion**: Tab completion for slash commands
- **Context Help**: Descriptions for each command
- **Parameter Support**: Commands with optional parameters
- **Error Handling**: Graceful handling of invalid commands

---

## File Operations & Tag System

### 1. File Tag Expansion

**File: `src/utils/file-tag-utils.ts` (63 lines)**

#### Tag Processing:
```typescript
export async function expandFileTags(text: string): Promise<string> {
  // Replace @file.txt with XML blocks containing file contents
}

export function collapseXmlBlocks(text: string): string {
  // Reverse operation: XML blocks back to @file.txt format
}
```

#### Features:
- **Automatic Expansion**: Convert `@file.txt` to XML blocks
- **Content Inclusion**: Include file contents in AI context
- **Error Handling**: Graceful handling of missing files
- **Bidirectional**: Convert between formats as needed

### 2. Advanced Patch System

**File: `src/utils/agent/apply-patch.ts` (816 lines)**

#### V4A Diff Format Support:
```typescript
// Unified diff processing with enhanced features
export function process_patch(
  patch: string,
  openFn: (path: string) => string,
  writeFn: (path: string, content: string) => void,
  removeFn: (path: string) => void,
): string
```

#### Patch Operations:
- **File Creation**: Create new files with content
- **File Updates**: Apply incremental changes
- **File Deletion**: Safe file removal
- **File Movement**: Rename and move operations
- **Conflict Resolution**: Handle merge conflicts intelligently

### 3. Git Integration

**File: `src/utils/get-diff.ts` (130 lines)**

#### Git Operations:
```typescript
export async function getGitDiff(
  workingDirectory: string = process.cwd()
): Promise<string> {
  // Generate git diff for current working directory
}
```

#### Features:
- **Status Checking**: Monitor git repository state
- **Diff Generation**: Create diffs for review
- **Branch Awareness**: Understand current branch context
- **Change Tracking**: Monitor file modifications

---

## Custom Hooks & UI Components

### 1. Custom Hooks System

**File: `src/hooks/use-confirmation.ts` (67 lines)**

#### Confirmation Queue Management:
```typescript
export function useConfirmation(): {
  submitConfirmation: (result: ConfirmationResult) => void;
  requestConfirmation: (
    prompt: HTMLElement,
    explanation?: string,
  ) => Promise<ConfirmationResult>;
  confirmationPrompt: HTMLElement | null;
  explanation?: string;
}
```

#### Features:
- **Queue-Based System**: Manages multiple confirmation requests
- **Promise-Based API**: Async confirmation handling
- **Explanation Support**: Optional explanations for confirmations
- **State Management**: Tracks current and pending confirmations

**File: `src/hooks/use-terminal-size.ts` (26 lines)**

#### Terminal Size Management:
```typescript
export function useTerminalSize(): { columns: number; rows: number } {
  const [size, setSize] = useState({
    columns: (process.stdout.columns || 60) - TERMINAL_PADDING_X,
    rows: process.stdout.rows || 20,
  });
}
```

#### Features:
- **Dynamic Resizing**: Responds to terminal resize events
- **Padding Calculation**: Accounts for terminal padding
- **Fallback Values**: Provides defaults when terminal size unavailable
- **Event Cleanup**: Proper event listener management

### 2. Advanced UI Components

#### Terminal Chat Input Thinking Component:
**File: `src/components/chat/terminal-chat-input-thinking.tsx` (129 lines)**

#### Features:
- **Animated Spinner**: Custom ball animation with elapsed time
- **Interrupt Handling**: Double-escape to interrupt operations
- **Raw Input Processing**: Handles collapsed escape sequences
- **Visual Feedback**: Shows thinking progress and interrupt instructions

#### Multiline Text Editor:
**File: `src/components/chat/multiline-editor.tsx` (400+ lines)**

#### Advanced Features:
- **Cross-Platform Input**: Handles different terminal escape sequences
- **Cursor Management**: Advanced cursor positioning and visibility
- **Text Buffer Integration**: Uses sophisticated text buffer system
- **Viewport Management**: Handles scrolling and text wrapping
- **Keyboard Shortcuts**: Emacs/readline-style shortcuts

### 3. Component Integration

#### Terminal Chat Command Review:
**File: `src/components/chat/terminal-chat-command-review.tsx` (200+ lines)**

#### Features:
- **Dynamic Options**: Context-aware approval options
- **Keyboard Shortcuts**: Single-key command approval
- **Explanation Mode**: Interactive command explanation
- **Custom Input**: Text input for custom denial messages
- **Approval Mode Switching**: Integrated approval mode management

---

## Vendor Components & Third-Party Integration

### 1. Terminal Component System

**Directory: `src/components/vendor/components/`**

#### Core Integration:
- **screen.js**: Main screen management with integration
- **list.js**: Enhanced list widget with keyboard navigation
- **input.js**: Advanced text input with completion support
- **box.js**: Layout containers with border styling
- **overlay.js**: Modal overlay system for dialogs

#### Features:
- **Keyboard Navigation**: Arrow key navigation with vi-style bindings
- **Visual Indicators**: Focus highlighting and selection states
- **Customizable Theming**: Color schemes and styling options
- **Widget Composition**: Composable UI elements for complex interfaces

### 2. Text Input Components

**File: `src/components/vendor/-text-input.tsx` (400+ lines)**

#### Advanced Text Input Features:
- **Cursor Management**: Advanced cursor positioning
- **Keyboard Shortcuts**: Comprehensive keyboard support
- **Line Continuation**: Bash-style line continuation with backslash
- **Text Selection**: Advanced text selection and manipulation
- **Paste Handling**: Intelligent paste text processing

### 3. Spinner Components

**File: `src/components/vendor/spinner.tsx` (36 lines)**

#### Custom Spinner System:
```typescript
const spinnerTypes: Record<string, string[]> = {
  dots: ["⢎ ", "⠎⠁", "⠊⠑", "⠈⠱", " ⡱", "⢀⡰", "⢄⡠", "⢆⡀"],
  ball: [
    "( ●    )",
    "(  ●   )",
    "(   ●  )",
    "(    ● )",
    "(     ●)",
    "(    ● )",
    "(   ●  )",
    "(  ●   )",
    "( ●    )",
    "(●     )",
  ],
};
```

#### Features:
- **Multiple Spinner Types**: Dots and ball animations
- **Configurable Speed**: Adjustable animation intervals
- **Integration**: Proper widget lifecycle management

### 4. Select Input System

**Directory: `src/components/select-input/`**

#### Components:
- **select-input.tsx**: Main select input component
- **indicator.tsx**: Selection indicators with Unicode symbols

#### Features:
- **Visual Indicators**: Uses Unicode symbols for consistent display
- **State Management**: Proper selection state handling
- **Accessibility**: Keyboard navigation support with screen reader compatibility

---

## Text Buffer & Advanced Input System

### 1. Sophisticated Text Buffer

**File: `src/text-buffer.ts` (977 lines)**

#### Core Text Buffer Features:
```typescript
export default class TextBuffer {
  private lines: Array<string> = [""];
  private cursorRow = 0;
  private cursorCol = 0;
  private scrollRow = 0;
  private scrollCol = 0;
  private version = 0;
  private undoStack: Array<UndoState> = [];
}
```

#### Advanced Capabilities:
- **Multi-line Editing**: Full multi-line text editing support
- **Undo/Redo System**: Comprehensive undo stack management
- **Viewport Management**: Scrolling and cursor visibility
- **Unicode Support**: Proper Unicode character handling
- **Cursor Positioning**: Advanced cursor movement and positioning
- **Text Manipulation**: Insert, delete, word operations

#### Keyboard Handling:
- **Emacs Shortcuts**: Ctrl+A, Ctrl+E, Ctrl+K, etc.
- **Arrow Key Navigation**: Full directional navigation
- **Word Operations**: Word-wise movement and deletion
- **Line Operations**: Line-wise operations and manipulation

### 2. Input Processing System

#### Multi-Platform Input Handling:
- **CSI-u Sequences**: Modern terminal escape sequence support
- **Legacy Sequences**: Backward compatibility with older terminals
- **Modifier Key Detection**: Shift, Ctrl, Alt key combinations
- **Raw Input Processing**: Direct stdin data processing

#### Text Processing Features:
- **Line Wrapping**: Intelligent text wrapping
- **Character Encoding**: Proper Unicode normalization
- **Input Validation**: Safe input processing and validation
- **Performance Optimization**: Efficient text buffer operations with rendering

---

## Example Projects & Templates

### 1. Example Project System

**Directory: `examples/`**

#### Project Structure:
```
example-name/
├── run.sh           # Launch script for new sessions
├── task.yaml        # Task specification and prompt
├── template/        # Starter files and instructions
└── runs/            # Generated work directories
```

#### Available Examples:

##### CameraASCII Project:
**Directory: `examples/camerascii/`**
- **Purpose**: Webcam feed to ASCII art conversion
- **Technology**: HTML5, JavaScript, Canvas API
- **Features**: Real-time video processing, ASCII character mapping

##### Build Codex Demo:
**Directory: `examples/build-codex-demo/`**
- **Purpose**: Recreate original 2021 Codex demonstration
- **Features**: Historical AI coding demonstration recreation

##### Impossible Pong:
**Directory: `examples/impossible-pong/`**
- **Purpose**: Progressive difficulty Pong game
- **Features**: Dynamic difficulty adjustment, game mechanics

##### Prompt Analyzer:
**Directory: `examples/prompt-analyzer/`**
- **Purpose**: Data science application for prompt clustering
- **Technology**: Data analysis, visualization, clustering algorithms
- **Features**: Prompt analysis, clustering visualization, statistical analysis

### 2. Template System

#### Template Features:
- **Starter Files**: Pre-configured project files
- **Documentation**: Markdown instructions and guides
- **Configuration**: Project-specific settings
- **Dependencies**: Required packages and tools

#### Run Script System:
```bash
#!/bin/bash
# Creates runs/run_N directory
# Copies template files
# Launches Kritrima AI CLI with task prompt
```

#### Features:
- **Incremental Runs**: Numbered run directories
- **Template Copying**: Automatic template file copying
- **Task Integration**: Seamless task prompt loading
- **Isolation**: Each run in separate directory

### 3. Prompting Guide

**File: `examples/prompting_guide.md` (118 lines)**

#### Comprehensive Prompting Documentation:
- **Best Practices**: Effective prompting strategies
- **Examples**: Real-world prompting examples
- **Techniques**: Advanced prompting techniques
- **Troubleshooting**: Common issues and solutions

---

## Advanced Features & Integrations

### 1. Desktop Notifications

#### macOS Integration:
```typescript
if (process.platform === "darwin") {
  spawn("osascript", [
    "-e",
    `display notification "${safePreview}" with title "${title}" subtitle "${cwd}" sound name "Ping"`,
  ]);
}
```

#### Features:
- **Response Notifications**: Alert when AI responds
- **Platform-Specific**: Native notification systems
- **Configurable**: User can enable/disable notifications
- **Context-Aware**: Include relevant information

### 2. Package Manager Detection

**File: `src/utils/package-manager-detector.ts` (74 lines)**

#### Auto-Detection:
```typescript
export async function detectInstallerByPath(): Promise<AgentName | undefined> {
  // Detect npm, pnpm, bun, yarn, deno based on installation path
}
```

#### Features:
- **Multiple Managers**: Support for npm, pnpm, bun, yarn, deno
- **Path Analysis**: Detect based on executable location
- **Update Commands**: Generate appropriate update commands
- **Fallback Logic**: Graceful handling of unknown managers

### 3. Update Checking System

**File: `src/utils/check-updates.ts` (147 lines)**

#### Automatic Updates:
```typescript
export async function checkForUpdates(): Promise<void> {
  // Check npm registry for newer versions
  // Display update notification if available
}
```

#### Features:
- **Registry Checking**: Query npm for latest version
- **Frequency Control**: Check once per day
- **User Notification**: Boxed update messages
- **Manager-Specific**: Appropriate update commands

### 4. Version Management

**File: `src/version.ts` (9 lines)**

#### Dynamic Version Loading:
```typescript
import pkg from "../package.json" with { type: "json" };
export const CLI_VERSION: string = (pkg as { version: string }).version;
```

#### Features:
- **Runtime Version Reading**: Always current version from package.json
- **Build Integration**: External package.json marking in build system
- **Version Consistency**: Single source of truth for version

### 5. Command Formatting System

**File: `src/format-command.ts` (54 lines)**

#### Intelligent Command Display:
```typescript
export function formatCommandForDisplay(command: Array<string>): string {
  // Handles bash -lc wrapper unwrapping
  // Proper shell quoting and escaping
  // User-friendly command presentation
}
```

#### Features:
- **Wrapper Detection**: Unwraps bash -lc command wrappers
- **Shell Quoting**: Proper shell quote handling
- **Display Optimization**: User-friendly command presentation
- **Error Handling**: Graceful fallback for malformed commands

### 6. Terminal Management

**File: `src/utils/terminal.ts` (85 lines)**

#### Terminal State Management:
```typescript
export function setInkRenderer(renderer: Instance): void;
export function clearTerminal(): void;
export function onExit(): void;
```

#### Features:
- **Integration**: Proper renderer management
- **Screen Clearing**: Terminal and scrollback clearing
- **Exit Handling**: Clean terminal state restoration
- **Debug Support**: FPS debugging for UI performance

### 7. Git Integration

**File: `src/utils/check-in-git.ts` (31 lines)**

#### Git Repository Detection:
```typescript
export function checkInGit(workdir: string): boolean {
  // Uses git rev-parse --is-inside-work-tree
  // Fast synchronous check
  // Reliable across Git versions
}
```

#### Features:
- **Repository Detection**: Reliable Git repository detection
- **Safety Checks**: Warn when outside Git repositories
- **Performance**: Fast synchronous operation
- **Cross-Platform**: Works across different Git configurations

---

## Conclusion

The Kritrima AI CLI represents a sophisticated integration of multiple complex systems working in harmony:

1. **Provider System**: Flexible, extensible AI provider integration with dynamic model discovery
2. **Agent Loop**: Autonomous AI orchestration with robust tool calling and state management
3. **Code Assistance**: Advanced file operations, shell execution, and project analysis
4. **Multi-Modal Interface**: Rich text and image input with real-time streaming responses
5. **Security Framework**: Comprehensive sandboxing and approval systems
6. **User Experience**: Intuitive terminal interface with powerful features
7. **Storage & Sessions**: Comprehensive session management and persistence
8. **Logging & Debugging**: Advanced debugging and monitoring infrastructure
9. **Bug Reporting**: Automated issue reporting and telemetry
10. **Advanced Features**: Desktop notifications, package management, auto-updates
11. **Storage & Session Management**: Persistent command history and session rollouts
12. **Logging & Debugging**: Comprehensive logging system with async queue processing
13. **Bug Reporting & Telemetry**: Automated GitHub issue generation with session data
14. **Full-Context & Single-Pass Mode**: Experimental directory-wide analysis and batch processing
15. **Command History & User Experience**: Advanced input system with file suggestions and slash commands
16. **File Operations & Tag System**: Sophisticated patch system and file tag expansion
17. **Custom Hooks & UI Components**: Reusable TypeScript hooks and advanced UI components
18. **Vendor Components & Third-Party Integration**: Custom components and third-party integrations
19. **Text Buffer & Advanced Input System**: Sophisticated multi-line text editing with Unicode support
20. **Example Projects & Templates**: Comprehensive example system with real-world projects
21. **Advanced Features & Integrations**: Desktop notifications, package detection, version management, and Git integration

Each component is designed to work independently while contributing to a cohesive, powerful AI coding assistant that can adapt to various workflows, providers, and security requirements. The system's modular architecture ensures maintainability, extensibility, and reliability across different environments and use cases.

The implementation demonstrates enterprise-grade software engineering practices with comprehensive error handling, extensive testing, performance optimization, and user experience considerations. The codebase is well-structured, thoroughly documented, and designed for long-term maintainability and extensibility.

### Key Architectural Strengths:

#### 1. **Modular Design**
- Independent, loosely-coupled components
- Clear separation of concerns
- Extensible plugin architecture
- Reusable utility functions and hooks

#### 2. **Advanced User Interface**
- Sophisticated terminal-based UI with TypeScript
- Custom text buffer with full Unicode support
- Advanced input handling with multi-platform compatibility
- Rich visual feedback and interactive components

#### 3. **Comprehensive Security**
- Multi-platform sandboxing (Landlock, Seatbelt, raw execution)
- Granular approval policies with session persistence
- Path validation and permission checking
- Safe command execution with timeout management

#### 4. **Robust State Management**
- Persistent session storage with rollout system
- Command history with sensitive data filtering
- Configuration management with multiple sources
- Context optimization and memory management

#### 5. **Developer Experience**
- Extensive example projects and templates
- Comprehensive prompting guide and documentation
- Advanced debugging and logging infrastructure
- Automated update checking and package management

#### 6. **Production Ready**
- Comprehensive error handling and recovery
- Performance monitoring and optimization
- Automated bug reporting and telemetry
- Cross-platform compatibility and testing

The Kritrima AI CLI stands as a testament to modern software engineering practices, combining cutting-edge AI capabilities with robust, maintainable, and user-friendly design. Its comprehensive feature set and modular architecture make it suitable for both individual developers and enterprise environments, while its extensive documentation and example projects ensure accessibility for users of all skill levels. 