/**
 * Advanced File Operations & Patch System
 *
 * Handles unified diff processing, file creation, editing, and deletion
 * Supports V4A diff format with conflict resolution and backup system
 */
import { readFileSync, writeFileSync, existsSync, unlinkSync, mkdirSync, copyFileSync } from 'fs';
import { dirname, join, resolve } from 'path';
import { logInfo, logError } from '../logger/log.js';
/**
 * Process patch string and apply changes
 */
export function processPatch(patch, workingDirectory = process.cwd(), createBackups = true) {
    const result = {
        success: true,
        operations: [],
        errors: [],
        backups: []
    };
    try {
        logInfo('Processing patch', { patchLength: patch.length, workingDirectory });
        // Parse the patch
        const diffs = parsePatch(patch);
        if (diffs.length === 0) {
            result.errors.push('No valid diffs found in patch');
            result.success = false;
            return result;
        }
        // Apply each diff
        for (const diff of diffs) {
            try {
                const operation = applyDiff(diff, workingDirectory, createBackups);
                result.operations.push(operation);
                if (operation.backup) {
                    result.backups.push(operation.backup);
                }
            }
            catch (error) {
                const errorMessage = error instanceof Error ? error.message : 'Unknown error';
                result.errors.push(`Failed to apply diff for ${diff.newFile}: ${errorMessage}`);
                result.success = false;
            }
        }
        logInfo('Patch processing completed', {
            operations: result.operations.length,
            errors: result.errors.length,
            success: result.success
        });
        return result;
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        logError('Patch processing failed', error instanceof Error ? error : new Error(errorMessage));
        result.errors.push(`Patch processing failed: ${errorMessage}`);
        result.success = false;
        return result;
    }
}
/**
 * Parse unified diff format
 */
export function parsePatch(patch) {
    const lines = patch.split('\n');
    const diffs = [];
    let currentDiff = null;
    let currentHunk = null;
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        // File header
        if (line.startsWith('--- ')) {
            if (currentDiff && currentHunk) {
                currentDiff.hunks.push(currentHunk);
            }
            if (currentDiff) {
                diffs.push(currentDiff);
            }
            currentDiff = {
                oldFile: line.substring(4).trim(),
                newFile: '',
                hunks: [],
                isNew: false,
                isDeleted: false
            };
            currentHunk = null;
        }
        else if (line.startsWith('+++ ') && currentDiff) {
            currentDiff.newFile = line.substring(4).trim();
            // Check for new/deleted files
            if (currentDiff.oldFile === '/dev/null') {
                currentDiff.isNew = true;
            }
            if (currentDiff.newFile === '/dev/null') {
                currentDiff.isDeleted = true;
            }
        }
        // Hunk header
        else if (line.startsWith('@@') && currentDiff) {
            if (currentHunk) {
                currentDiff.hunks.push(currentHunk);
            }
            const hunkMatch = line.match(/@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@/);
            if (hunkMatch) {
                currentHunk = {
                    oldStart: parseInt(hunkMatch[1]),
                    oldCount: parseInt(hunkMatch[2] || '1'),
                    newStart: parseInt(hunkMatch[3]),
                    newCount: parseInt(hunkMatch[4] || '1'),
                    lines: []
                };
            }
        }
        // Hunk content
        else if (currentHunk && (line.startsWith(' ') || line.startsWith('-') || line.startsWith('+'))) {
            currentHunk.lines.push(line);
        }
        // V4A format support
        else if (line.startsWith('*** [') && line.includes('] File: ')) {
            // Parse V4A format: *** [ACTION] File: path/to/file
            const match = line.match(/\*\*\* \[(\w+)\] File: (.+)/);
            if (match) {
                const action = match[1].toLowerCase();
                const filePath = match[2].trim();
                if (currentDiff && currentHunk) {
                    currentDiff.hunks.push(currentHunk);
                }
                if (currentDiff) {
                    diffs.push(currentDiff);
                }
                currentDiff = {
                    oldFile: action === 'create' ? '/dev/null' : filePath,
                    newFile: action === 'delete' ? '/dev/null' : filePath,
                    hunks: [],
                    isNew: action === 'create',
                    isDeleted: action === 'delete'
                };
                currentHunk = null;
            }
        }
    }
    // Add final diff and hunk
    if (currentDiff && currentHunk) {
        currentDiff.hunks.push(currentHunk);
    }
    if (currentDiff) {
        diffs.push(currentDiff);
    }
    return diffs;
}
/**
 * Apply a single diff to a file
 */
function applyDiff(diff, workingDirectory, createBackups) {
    const filePath = resolve(workingDirectory, diff.newFile);
    // Handle file deletion
    if (diff.isDeleted) {
        if (!existsSync(filePath)) {
            throw new Error(`File to delete does not exist: ${filePath}`);
        }
        let backupPath;
        if (createBackups) {
            backupPath = createBackup(filePath);
        }
        unlinkSync(filePath);
        return {
            type: 'delete',
            path: filePath,
            backup: backupPath
        };
    }
    // Handle new file creation
    if (diff.isNew) {
        if (existsSync(filePath)) {
            throw new Error(`File already exists: ${filePath}`);
        }
        // Ensure directory exists
        const dir = dirname(filePath);
        if (!existsSync(dir)) {
            mkdirSync(dir, { recursive: true });
        }
        // Extract content from hunks
        const content = extractNewFileContent(diff.hunks);
        writeFileSync(filePath, content, 'utf-8');
        return {
            type: 'create',
            path: filePath,
            content
        };
    }
    // Handle file editing
    if (!existsSync(filePath)) {
        throw new Error(`File to edit does not exist: ${filePath}`);
    }
    let backupPath;
    if (createBackups) {
        backupPath = createBackup(filePath);
    }
    const originalContent = readFileSync(filePath, 'utf-8');
    const modifiedContent = applyHunks(originalContent, diff.hunks);
    writeFileSync(filePath, modifiedContent, 'utf-8');
    return {
        type: 'edit',
        path: filePath,
        content: modifiedContent,
        backup: backupPath
    };
}
/**
 * Extract content for new file from hunks
 */
function extractNewFileContent(hunks) {
    const lines = [];
    for (const hunk of hunks) {
        for (const line of hunk.lines) {
            if (line.startsWith('+')) {
                lines.push(line.substring(1));
            }
            else if (line.startsWith(' ')) {
                lines.push(line.substring(1));
            }
        }
    }
    return lines.join('\n');
}
/**
 * Apply hunks to existing file content
 */
function applyHunks(originalContent, hunks) {
    const originalLines = originalContent.split('\n');
    let modifiedLines = [...originalLines];
    let offset = 0;
    // Sort hunks by line number
    const sortedHunks = [...hunks].sort((a, b) => a.oldStart - b.oldStart);
    for (const hunk of sortedHunks) {
        const result = applyHunk(modifiedLines, hunk, offset);
        modifiedLines = result.lines;
        offset += result.offset;
    }
    return modifiedLines.join('\n');
}
/**
 * Apply a single hunk to file lines
 */
function applyHunk(lines, hunk, currentOffset) {
    const startLine = hunk.oldStart - 1 + currentOffset; // Convert to 0-based index
    const newLines = [];
    let oldLineIndex = 0;
    let newOffset = 0;
    for (const line of hunk.lines) {
        if (line.startsWith(' ')) {
            // Context line - should match
            const expectedLine = line.substring(1);
            const actualLine = lines[startLine + oldLineIndex];
            if (actualLine !== expectedLine) {
                logError('Hunk context mismatch', new Error(`Expected: "${expectedLine}", Got: "${actualLine}"`));
                // Continue anyway - might be whitespace differences
            }
            newLines.push(expectedLine);
            oldLineIndex++;
        }
        else if (line.startsWith('-')) {
            // Line to remove
            oldLineIndex++;
            newOffset--;
        }
        else if (line.startsWith('+')) {
            // Line to add
            newLines.push(line.substring(1));
            newOffset++;
        }
    }
    // Replace the section
    const result = [
        ...lines.slice(0, startLine),
        ...newLines,
        ...lines.slice(startLine + oldLineIndex)
    ];
    return {
        lines: result,
        offset: currentOffset + newOffset
    };
}
/**
 * Create backup of file
 */
function createBackup(filePath) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${filePath}.backup-${timestamp}`;
    copyFileSync(filePath, backupPath);
    logInfo('Created backup', { original: filePath, backup: backupPath });
    return backupPath;
}
/**
 * Restore file from backup
 */
export function restoreFromBackup(backupPath) {
    try {
        if (!existsSync(backupPath)) {
            return false;
        }
        // Extract original path from backup path
        const originalPath = backupPath.replace(/\.backup-[\d-T]+$/, '');
        copyFileSync(backupPath, originalPath);
        logInfo('Restored from backup', { backup: backupPath, original: originalPath });
        return true;
    }
    catch (error) {
        logError('Failed to restore from backup', error instanceof Error ? error : new Error(String(error)));
        return false;
    }
}
/**
 * Clean up backup files
 */
export function cleanupBackups(directory, maxAge = 7 * 24 * 60 * 60 * 1000) {
    try {
        const { readdirSync, statSync } = require('fs');
        const files = readdirSync(directory);
        let cleaned = 0;
        const now = Date.now();
        for (const file of files) {
            if (file.includes('.backup-')) {
                const filePath = join(directory, file);
                const stats = statSync(filePath);
                if (now - stats.mtime.getTime() > maxAge) {
                    unlinkSync(filePath);
                    cleaned++;
                }
            }
        }
        logInfo('Cleaned up backup files', { directory, cleaned });
        return cleaned;
    }
    catch (error) {
        logError('Failed to cleanup backups', error instanceof Error ? error : new Error(String(error)));
        return 0;
    }
}
//# sourceMappingURL=apply-patch.js.map