#!/usr/bin/env node

console.log('Testing CLI...');

try {
  const { CLI_VERSION } = await import('./dist/version.js');
  console.log('Version:', CLI_VERSION);
} catch (error) {
  console.error('Error importing version:', error.message);
}

try {
  const { getAvailableProviders } = await import('./dist/utils/providers.js');
  const providers = getAvailableProviders();
  console.log('Available providers:', providers);
} catch (error) {
  console.error('Error importing providers:', error.message);
}

console.log('CLI test completed.');
