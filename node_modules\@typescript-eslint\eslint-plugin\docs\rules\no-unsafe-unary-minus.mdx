---
description: 'Require unary negation to take a number.'
---

import Tabs from '@theme/Tabs';
import TabItem from '@theme/TabItem';

> 🛑 This file is source code, not the primary documentation location! 🛑
>
> See **https://typescript-eslint.io/rules/no-unsafe-unary-minus** for documentation.

TypeScript does not prevent you from putting a minus sign before things other than numbers:

```ts
const s = 'hello';
const x = -s; // x is NaN
```

This rule restricts the unary `-` operator to `number | bigint`.

## Examples

<Tabs>
<TabItem value="❌ Incorrect">

```ts
declare const a: string;
-a;

declare const b: {};
-b;
```

</TabItem>
<TabItem value="✅ Correct">

```ts
-42;
-42n;

declare const a: number;
-a;

declare const b: number;
-b;

declare const c: number | bigint;
-c;

declare const d: any;
-d;

declare const e: 1 | 2;
-e;
```

</TabItem>
</Tabs>

{/* Intentionally Omitted: When Not To Use It */}
