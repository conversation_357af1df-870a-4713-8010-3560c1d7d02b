/**
 * Linux Landlock Sandbox Implementation
 *
 * Uses Linux Landlock LSM for filesystem access control
 * Provides secure command execution with restricted file system access
 */
import type { ExecInput, ExecResult, AppConfig } from '../../../types/index.js';
/**
 * Execute command with Landlock sandboxing
 */
export declare function execWithLandlock(input: ExecInput, config: AppConfig, additionalWritableRoots?: ReadonlyArray<string>): Promise<ExecResult>;
/**
 * Test Landlock functionality
 */
export declare function testLandlock(): Promise<{
    available: boolean;
    version?: string;
    capabilities: string[];
    limitations: string[];
}>;
/**
 * Get Landlock execution environment info
 */
export declare function getLandlockEnvironment(): {
    platform: string;
    sandboxing: boolean;
    restrictions: string[];
};
//# sourceMappingURL=landlock.d.ts.map